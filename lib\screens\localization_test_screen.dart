import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/localization_service.dart';
import '../widgets/language_selector.dart';
import '../utils/localization_helper.dart';

class LocalizationTestScreen extends StatelessWidget {
  const LocalizationTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.language),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sélecteur de langue
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.language,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    const LanguageSelector(),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Test des traductions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test des traductions',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    _buildTestRow(context, 'App Title', l10n.appTitle),
                    _buildTestRow(context, 'Home', l10n.home),
                    _buildTestRow(context, 'Settings', l10n.settings),
                    _buildTestRow(context, 'Vehicle', l10n.vehicle),
                    _buildTestRow(context, 'Maintenance', l10n.maintenance),
                    _buildTestRow(context, 'Add', l10n.add),
                    _buildTestRow(context, 'Edit', l10n.edit),
                    _buildTestRow(context, 'Delete', l10n.delete),
                    _buildTestRow(context, 'Save', l10n.save),
                    _buildTestRow(context, 'Cancel', l10n.cancel),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Test de direction RTL/LTR
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test de direction',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    Consumer<LocalizationService>(
                      builder: (context, localizationService, child) {
                        return Column(
                          children: [
                            Row(
                              children: [
                                Text('Direction actuelle: '),
                                Text(
                                  localizationService.isRTL ? 'RTL (Droite vers Gauche)' : 'LTR (Gauche vers Droite)',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Text('Langue actuelle: '),
                                Text(
                                  localizationService.getCurrentLanguageName(),
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Test de formatage
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test de formatage',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    
                    _buildTestRow(context, 'Date', LocalizationHelper.formatDate(context, DateTime.now())),
                    _buildTestRow(context, 'Heure', LocalizationHelper.formatTime(context, TimeOfDay.now())),
                    _buildTestRow(context, 'Mois', LocalizationHelper.getMonthName(context, DateTime.now().month)),
                    _buildTestRow(context, 'Jour', LocalizationHelper.getDayName(context, DateTime.now().weekday)),
                    _buildTestRow(context, 'Devise', LocalizationHelper.formatCurrency(context, 1234.56, 'DZD')),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTestRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              textAlign: LocalizationHelper.getTextAlign(context),
            ),
          ),
        ],
      ),
    );
  }
}