import 'package:flutter/material.dart';
import '../models/vehicle_config.dart';
import '../models/maintenance_item.dart';
import '../models/notification_settings.dart';

class PredictiveService {
  /// Calcule le kilométrage prévisionnel basé sur la distance quotidienne
  static double calculatePredictiveKilometers({
    required VehicleConfig vehicle,
    required double dailyDistanceKm,
    DateTime? targetDate,
  }) {
    final now = DateTime.now();
    final lastUpdate = vehicle.lastKmUpdate;
    final daysSinceLastUpdate = now.difference(lastUpdate).inDays;
    
    // Kilométrage prévisionnel = kilométrage actuel + (jours écoulés × distance quotidienne)
    final predictiveKm = vehicle.currentKilometers + (daysSinceLastUpdate * dailyDistanceKm);
    
    // Si une date cible est spécifiée, calculer jusqu'à cette date
    if (targetDate != null) {
      final daysUntilTarget = targetDate.difference(now).inDays;
      return predictiveKm + (daysUntilTarget * dailyDistanceKm);
    }
    
    return predictiveKm;
  }

  /// Calcule la différence entre le kilométrage réel et prévisionnel
  static double calculateKilometersDifference({
    required VehicleConfig vehicle,
    required double dailyDistanceKm,
  }) {
    final predictiveKm = calculatePredictiveKilometers(
      vehicle: vehicle,
      dailyDistanceKm: dailyDistanceKm,
    );
    
    return predictiveKm - vehicle.currentKilometers;
  }

  /// Calcule quand une opération de maintenance sera due (prévisionnel)
  static DateTime? calculatePredictiveMaintenanceDate({
    required MaintenanceItem item,
    required VehicleConfig vehicle,
    required double dailyDistanceKm,
  }) {
    if (item.defaultIntervalKm <= 0) return null;
    
    final kmSinceLastMaintenance = vehicle.currentKilometers - item.lastMaintenanceKm;
    final kmUntilNextMaintenance = item.defaultIntervalKm - kmSinceLastMaintenance;
    
    if (kmUntilNextMaintenance <= 0) {
      // Maintenance déjà due
      return DateTime.now();
    }
    
    // Calculer combien de jours il faut pour parcourir les km restants
    final daysUntilMaintenance = (kmUntilNextMaintenance / dailyDistanceKm).ceil();
    
    return DateTime.now().add(Duration(days: daysUntilMaintenance));
  }

  /// Vérifie si une mise à jour du kilométrage est recommandée
  static bool shouldUpdateKilometers({
    required VehicleConfig vehicle,
    required double dailyDistanceKm,
    int thresholdDays = 7, // Recommander mise à jour après 7 jours
  }) {
    final daysSinceLastUpdate = DateTime.now().difference(vehicle.lastKmUpdate).inDays;
    return daysSinceLastUpdate >= thresholdDays;
  }

  /// Calcule le kilométrage recommandé pour la mise à jour
  static double getRecommendedKilometers({
    required VehicleConfig vehicle,
    required double dailyDistanceKm,
  }) {
    return calculatePredictiveKilometers(
      vehicle: vehicle,
      dailyDistanceKm: dailyDistanceKm,
    );
  }

  /// Génère des suggestions de maintenance basées sur les prévisions
  static List<MaintenancePrediction> generateMaintenancePredictions({
    required List<MaintenanceItem> items,
    required VehicleConfig vehicle,
    required double dailyDistanceKm,
    int daysAhead = 90, // Prédictions pour les 90 prochains jours
  }) {
    final predictions = <MaintenancePrediction>[];
    
    for (final item in items) {
      if (!item.isActive || item.defaultIntervalKm <= 0) continue;
      // Exclure les entretiens de type "autre" et les entretiens personnalisés
      if (item.category.toLowerCase() == 'autre') continue;
      if (item.name.startsWith('[CUSTOM]')) continue;
      
      final predictiveDate = calculatePredictiveMaintenanceDate(
        item: item,
        vehicle: vehicle,
        dailyDistanceKm: dailyDistanceKm,
      );
      
      if (predictiveDate != null) {
        final daysUntil = predictiveDate.difference(DateTime.now()).inDays;
        
        if (daysUntil <= daysAhead) {
          final predictiveKm = calculatePredictiveKilometers(
            vehicle: vehicle,
            dailyDistanceKm: dailyDistanceKm,
            targetDate: predictiveDate,
          );
          
          predictions.add(MaintenancePrediction(
            item: item,
            predictedDate: predictiveDate,
            predictedKilometers: predictiveKm,
            daysUntil: daysUntil,
            confidence: _calculateConfidence(daysUntil),
          ));
        }
      }
    }
    
    // Trier par date prévue
    predictions.sort((a, b) => a.predictedDate.compareTo(b.predictedDate));
    
    return predictions;
  }

  /// Calcule le niveau de confiance de la prédiction
  static double _calculateConfidence(int daysUntil) {
    if (daysUntil <= 7) return 0.95; // Très haute confiance
    if (daysUntil <= 30) return 0.85; // Haute confiance
    if (daysUntil <= 60) return 0.75; // Confiance moyenne
    return 0.65; // Confiance faible
  }

  /// Vérifie si les prédictions sont fiables
  static bool arePredictionsReliable({
    required VehicleConfig vehicle,
    required double dailyDistanceKm,
    int maxDaysSinceUpdate = 14,
  }) {
    final daysSinceUpdate = DateTime.now().difference(vehicle.lastKmUpdate).inDays;
    return daysSinceUpdate <= maxDaysSinceUpdate && dailyDistanceKm > 0;
  }
}

class MaintenancePrediction {
  final MaintenanceItem item;
  final DateTime predictedDate;
  final double predictedKilometers;
  final int daysUntil;
  final double confidence;

  const MaintenancePrediction({
    required this.item,
    required this.predictedDate,
    required this.predictedKilometers,
    required this.daysUntil,
    required this.confidence,
  });

  String get confidenceText {
    if (confidence >= 0.9) return 'Très fiable';
    if (confidence >= 0.8) return 'Fiable';
    if (confidence >= 0.7) return 'Modérée';
    return 'Approximative';
  }

  Color get confidenceColor {
    if (confidence >= 0.9) return const Color(0xFF10B981); // Vert
    if (confidence >= 0.8) return const Color(0xFF3B82F6); // Bleu
    if (confidence >= 0.7) return const Color(0xFFF59E0B); // Orange
    return const Color(0xFFEF4444); // Rouge
  }
}
