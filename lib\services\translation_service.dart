import '../l10n/app_localizations.dart';

class TranslationService {
  static final TranslationService _instance = TranslationService._internal();
  factory TranslationService() => _instance;
  TranslationService._internal();

  /// Traduit le nom d'un élément de maintenance
  String translateMaintenanceName(String frenchName, AppLocalizations l10n) {
    switch (frenchName) {
      case 'Vidange Huile Moteur':
        return l10n.oilChangeEngine;
      case 'Filtre à Gasoil':
        return l10n.fuelFilter;
      case 'Filtre à Air':
        return l10n.airFilter;
      case 'Filtre Habitacle':
        return l10n.cabinFilter;
      case 'Courroie de Distribution':
        return l10n.timingBelt;
      case 'Liquide de Refroidissement':
        return l10n.coolantFluid;
      case 'Liquide de Frein':
        return l10n.brakeFluid;
      case 'Plaquettes de Frein':
        return l10n.brakePads;
      case 'Pneus':
        return l10n.tiresItem;
      default:
        return frenchName; // Retourne le nom original si pas de traduction
    }
  }

  /// Traduit la description d'un élément de maintenance
  String translateMaintenanceDescription(String frenchName, AppLocalizations l10n) {
    switch (frenchName) {
      case 'Vidange Huile Moteur':
        return l10n.oilChangeEngineDesc;
      case 'Filtre à Gasoil':
        return l10n.fuelFilterDesc;
      case 'Filtre à Air':
        return l10n.airFilterDesc;
      case 'Filtre Habitacle':
        return l10n.cabinFilterDesc;
      case 'Courroie de Distribution':
        return l10n.timingBeltDesc;
      case 'Liquide de Refroidissement':
        return l10n.coolantFluidDesc;
      case 'Liquide de Frein':
        return l10n.brakeFluidDesc;
      case 'Plaquettes de Frein':
        return l10n.brakePadsDesc;
      case 'Pneus':
        return l10n.tiresItemDesc;
      default:
        return frenchName; // Retourne le nom original si pas de traduction
    }
  }

  /// Traduit le nom d'un document
  String translateDocumentName(String frenchName, AppLocalizations l10n) {
    switch (frenchName) {
      case 'Assurance':
        return l10n.insuranceDoc;
      case 'Contrôle technique':
        return l10n.technicalControlDoc;
      case 'Extincteur':
        return l10n.extinguisherDoc;
      default:
        return frenchName; // Retourne le nom original si pas de traduction
    }
  }

  /// Traduit la description d'un document
  String translateDocumentDescription(String frenchName, AppLocalizations l10n) {
    switch (frenchName) {
      case 'Assurance':
        return l10n.insuranceDocDesc;
      case 'Contrôle technique':
        return l10n.technicalControlDocDesc;
      case 'Extincteur':
        return l10n.extinguisherDocDesc;
      default:
        return frenchName; // Retourne le nom original si pas de traduction
    }
  }

  /// Traduit le nom d'une catégorie
  String translateCategoryName(String frenchCategory, AppLocalizations l10n) {
    switch (frenchCategory) {
      case 'Moteur':
        return l10n.engineCategory;
      case 'Filtres':
        return l10n.filtersCategory;
      case 'Freinage':
        return l10n.brakingCategory;
      case 'Pneumatiques':
        return l10n.tiresCategory;
      case 'Refroidissement':
        return l10n.coolingCategory;
      case 'Confort':
        return l10n.comfortCategory;
      case 'Assurance':
        return l10n.insuranceCategory;
      case 'Contrôle':
        return l10n.controlCategory;
      case 'Sécurité':
        return l10n.securityCategory;
      default:
        return frenchCategory; // Retourne le nom original si pas de traduction
    }
  }
}
