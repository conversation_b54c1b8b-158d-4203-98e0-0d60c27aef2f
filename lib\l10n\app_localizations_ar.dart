import 'app_localizations.dart';

class AppLocalizationsAr extends AppLocalizations {
  // Navigation et interface générale
  @override
  String get appTitle => 'كاروستي';
  @override
  String get home => 'الرئيسية';
  @override
  String get settings => 'الإعدادات';
  @override
  String get notifications => 'الإشعارات';
  @override
  String get statistics => 'الإحصائيات';
  @override
  String get about => 'حول التطبيق';
  @override
  String get help => 'المساعدة';
  @override
  String get language => 'اللغة';
  @override
  String get theme => 'المظهر';
  @override
  String get currency => 'العملة';
  @override
  String get premium => 'المميز';
  @override
  String get backup => 'النسخ الاحتياطي';
  @override
  String get restore => 'الاستعادة';
  @override
  String get export => 'تصدير';
  @override
  String get import => 'استيراد';
  @override
  String get cancel => 'إلغاء';
  @override
  String get save => 'حفظ';
  @override
  String get delete => 'حذف';
  @override
  String get edit => 'تعديل';
  @override
  String get add => 'إضافة';
  @override
  String get confirm => 'تأكيد';
  @override
  String get yes => 'نعم';
  @override
  String get no => 'لا';
  @override
  String get ok => 'موافق';
  @override
  String get close => 'إغلاق';
  @override
  String get back => 'رجوع';
  @override
  String get next => 'التالي';
  @override
  String get previous => 'السابق';
  @override
  String get loading => 'جاري التحميل...';
  @override
  String get error => 'خطأ';
  @override
  String get success => 'نجح';
  @override
  String get warning => 'تحذير';
  @override
  String get info => 'معلومات';

  // Véhicule
  @override
  String get vehicle => 'المركبة';
  @override
  String get vehicles => 'المركبات';
  @override
  String get addVehicle => 'إضافة مركبة';
  @override
  String get editVehicle => 'تعديل المركبة';
  @override
  String get deleteVehicle => 'حذف المركبة';
  @override
  String get vehicleName => 'اسم المركبة';
  @override
  String get brand => 'الماركة';
  @override
  String get model => 'الطراز';
  @override
  String get year => 'السنة';
  @override
  String get currentKilometers => 'الكيلومترات الحالية';
  @override
  String get licensePlate => 'رقم اللوحة';
  @override
  String get engineType => 'نوع المحرك';
  @override
  String get selectVehicle => 'اختر المركبة';
  @override
  String get noVehicleSelected => 'لم يتم اختيار مركبة';
  @override
  String get defaultVehicle => 'المركبة الافتراضية';
  @override
  String get switchVehicle => 'تبديل المركبة';
  @override
  String get vehicleInfo => 'معلومات المركبة';
  @override
  String get updateKilometers => 'تحديث الكيلومترات';
  @override
  String get enterKilometers => 'أدخل الكيلومترات';
  @override
  String get kilometersUpdated => 'تم تحديث الكيلومترات';
  @override
  String get invalidKilometers => 'كيلومترات غير صحيحة';

  // Entretien
  @override
  String get maintenance => 'الصيانة';
  @override
  String get maintenanceItems => 'عناصر الصيانة';
  @override
  String get addMaintenance => 'إضافة صيانة';
  @override
  String get editMaintenance => 'تعديل الصيانة';
  @override
  String get deleteMaintenance => 'حذف الصيانة';
  @override
  String get maintenanceName => 'اسم الصيانة';
  @override
  String get maintenanceDescription => 'وصف الصيانة';
  @override
  String get category => 'الفئة';
  @override
  String get interval => 'الفترة';
  @override
  String get defaultInterval => 'الفترة الافتراضية';
  @override
  String get customInterval => 'فترة مخصصة';
  @override
  String get lastMaintenance => 'آخر صيانة';
  @override
  String get nextMaintenance => 'الصيانة القادمة';
  @override
  String get maintenanceDue => 'صيانة مستحقة';
  @override
  String get maintenanceApproaching => 'صيانة قريبة';
  @override
  String get maintenanceUpToDate => 'الصيانة محدثة';
  @override
  String get performMaintenance => 'تنفيذ الصيانة';
  @override
  String get maintenanceCompleted => 'تمت الصيانة';
  @override
  String get maintenanceHistory => 'تاريخ الصيانة';
  @override
  String get maintenanceDate => 'تاريخ الصيانة';
  @override
  String get maintenanceCost => 'تكلفة الصيانة';
  @override
  String get maintenanceLocation => 'مكان الصيانة';
  @override
  String get mechanicName => 'اسم الميكانيكي';
  @override
  String get notes => 'ملاحظات';
  @override
  String get kmUntilNext => 'كم حتى التالية';
  @override
  String get usagePercentage => 'نسبة الاستخدام';
  @override
  String get active => 'نشط';
  @override
  String get inactive => 'غير نشط';

  // Catégories d'entretien
  @override
  String get engine => 'المحرك';
  @override
  String get brakes => 'الفرامل';
  @override
  String get tires => 'الإطارات';
  @override
  String get fluids => 'السوائل';
  @override
  String get filters => 'المرشحات';
  @override
  String get electrical => 'الكهرباء';
  @override
  String get bodywork => 'الهيكل';
  @override
  String get interior => 'الداخلية';
  @override
  String get safety => 'الأمان';
  @override
  String get other => 'أخرى';

  // Documents
  @override
  String get documents => 'الوثائق';
  @override
  String get addDocument => 'إضافة وثيقة';
  @override
  String get editDocument => 'تعديل الوثيقة';
  @override
  String get deleteDocument => 'حذف الوثيقة';
  @override
  String get documentName => 'اسم الوثيقة';
  @override
  String get documentDescription => 'وصف الوثيقة';
  @override
  String get expirationDate => 'تاريخ الانتهاء';
  @override
  String get companyName => 'اسم الشركة';
  @override
  String get documentNumber => 'رقم الوثيقة';
  @override
  String get documentExpired => 'وثيقة منتهية الصلاحية';
  @override
  String get documentExpiring => 'وثيقة قاربت على الانتهاء';
  @override
  String get documentValid => 'وثيقة صالحة';
  @override
  String get insurance => 'التأمين';
  @override
  String get registration => 'التسجيل';
  @override
  String get technicalInspection => 'الفحص الفني';
  @override
  String get warranty => 'الضمان';
  @override
  String get invoice => 'الفاتورة';
  @override
  String get receipt => 'الإيصال';
  @override
  String get manual => 'الدليل';
  @override
  String get certificate => 'الشهادة';

  // Statistiques
  @override
  String get totalMaintenanceItems => 'إجمالي عناصر الصيانة';
  @override
  String get itemsDue => 'العناصر المستحقة';
  @override
  String get itemsApproaching => 'العناصر القريبة';
  @override
  String get itemsUpToDate => 'العناصر المحدثة';
  @override
  String get overview => 'نظرة عامة';
  @override
  String get quickActions => 'إجراءات سريعة';
  @override
  String get urgent => 'عاجل';
  @override
  String get upcoming => 'قادم';
  @override
  String get upToDate => 'محدث';
  @override
  String get toProcess => 'للمعالجة';
  @override
  String get toPlan => 'للتخطيط';
  @override
  String get inOrder => 'منتظم';
  @override
  String get configureVehicle => 'إعداد المركبة';
  @override
  String get configure => 'إعداد';
  @override
  String get updateKm => 'تحديث كم';
  @override
  String get update => 'تحديث';
  @override
  String get diagnostics => 'التشخيص';
  @override
  String get analyze => 'تحليل';
  @override
  String get privacyPolicy => 'سياسة الخصوصية';
  @override
  String get importExport => 'استيراد/تصدير';
  @override
  String get updateApp => 'تحديث التطبيق';
  @override
  String get rateApp => 'تقييم التطبيق';
  @override
  String get quit => 'خروج';
  @override
  String get hello => 'مرحبا!';
  @override
  String get version => 'الإصدار';
  @override
  String get newVehicle => 'مركبة جديدة';
  @override
  String get vehicleConfiguration => 'إعداد المركبة';
  @override
  String get information => 'المعلومات';
  @override
  String get nameRequired => 'الاسم مطلوب';
  @override
  String get brandRequired => 'الماركة مطلوبة';
  @override
  String get modelRequired => 'الطراز مطلوب';
  @override
  String get yearRequired => 'السنة مطلوبة';
  @override
  String get invalidYear => 'سنة غير صحيحة';
  @override
  String get technical => 'تقني';
  @override
  String get kilometersRequired => 'الكيلومترات مطلوبة';
  @override
  String get saving => 'جاري الحفظ...';
  @override
  String get deleteVehicleTitle => '⚠️ حذف المركبة';
  @override
  String get deleteVehicleMessage => 'هذا الإجراء سيحذف نهائياً:\n\n'
      '• المركبة وإعداداتها\n'
      '• كامل تاريخ الصيانة\n'
      '• جميع بيانات التطبيق\n\n'
      'هذا الإجراء غير قابل للإلغاء!';
  @override
  String get history => 'التاريخ';
  @override
  String get all => 'الكل';
  @override
  String get search => 'بحث';
  @override
  String get filter => 'تصفية';
  @override
  String get searchInHistory => 'البحث في التاريخ...';
  @override
  String get performed => 'تم التنفيذ';
  @override
  String get editKilometers => 'تعديل الكيلومترات';
  @override
  String get deleteThisMaintenance => 'حذف هذه الصيانة';
  @override
  String get cost => 'التكلفة';
  @override
  String get location => 'الموقع';
  @override
  String get mechanic => 'الميكانيكي';
  @override
  String get noHistoryFound => 'لم يتم العثور على تاريخ';
  @override
  String get maintenanceWillAppearHere => 'ستظهر أعمال الصيانة المنجزة هنا';
  @override
  String get deletedMaintenance => 'صيانة محذوفة';
  @override
  String get deleteMaintenanceTitle => 'حذف الصيانة';
  @override
  String get deleteMaintenanceConfirmation => 'هل أنت متأكد من أنك تريد حذف هذه الصيانة؟';
  @override
  String get thisActionCannotBeUndone => 'لا يمكن التراجع عن هذا الإجراء.';
  @override
  String get soon => 'قريباً';
  @override
  String get progression => 'التقدم';
  @override
  String get currentKm => 'الكم الحالي';
  @override
  String get last => 'الأخير';
  @override
  String get remaining => 'المتبقي';
  @override
  String get exceededBy => 'تجاوز بـ';
  @override
  String get actions => 'الإجراءات';
  @override
  String get perform => 'تنفيذ';
  @override
  String get noHistory => 'لا يوجد تاريخ';
  @override
  String get totalCost => 'التكلفة الإجمالية';
  @override
  String get averageCost => 'متوسط التكلفة';
  @override
  String get lastUpdate => 'آخر تحديث';
  @override
  String get maintenanceOverview => 'نظرة عامة على الصيانة';
  @override
  String get costAnalysis => 'تحليل التكلفة';
  @override
  String get monthlyStats => 'الإحصائيات الشهرية';
  @override
  String get yearlyStats => 'الإحصائيات السنوية';
  @override
  String get mostExpensive => 'الأغلى';
  @override
  String get mostFrequent => 'الأكثر تكراراً';
  @override
  String get upcomingMaintenance => 'الصيانة القادمة';

  // Notifications
  @override
  String get notificationTitle => 'عنوان الإشعار';
  @override
  String get notificationBody => 'محتوى الإشعار';
  @override
  String get enableNotifications => 'تفعيل الإشعارات';
  @override
  String get notificationSettings => 'إعدادات الإشعارات';
  @override
  String get dailyReminder => 'تذكير يومي';
  @override
  String get weeklyReminder => 'تذكير أسبوعي';
  @override
  String get monthlyReminder => 'تذكير شهري';
  @override
  String get maintenanceReminder => 'تذكير الصيانة';
  @override
  String get documentReminder => 'تذكير الوثائق';
  @override
  String get reminderTime => 'وقت التذكير';
  @override
  String get noNotifications => 'لا توجد إشعارات';
  @override
  String get markAsRead => 'تحديد كمقروء';
  @override
  String get clearAll => 'مسح الكل';

  // Erreurs et messages
  @override
  String get errorLoadingData => 'خطأ في تحميل البيانات';
  @override
  String get errorSavingData => 'خطأ في حفظ البيانات';
  @override
  String get errorDeletingData => 'خطأ في حذف البيانات';
  @override
  String get errorInvalidInput => 'مدخل غير صحيح';
  @override
  String get errorNetworkConnection => 'خطأ في الاتصال بالشبكة';
  @override
  String get errorDatabaseConnection => 'خطأ في الاتصال بقاعدة البيانات';
  @override
  String get errorPermissionDenied => 'تم رفض الإذن';
  @override
  String get errorFileNotFound => 'الملف غير موجود';
  @override
  String get errorUnknown => 'خطأ غير معروف';
  @override
  String get dataLoadedSuccessfully => 'تم تحميل البيانات بنجاح';
  @override
  String get dataSavedSuccessfully => 'تم حفظ البيانات بنجاح';
  @override
  String get dataDeletedSuccessfully => 'تم حذف البيانات بنجاح';
  @override
  String get operationCompleted => 'تمت العملية';
  @override
  String get operationFailed => 'فشلت العملية';
  @override
  String get confirmDelete => 'تأكيد الحذف';
  @override
  String get confirmDeleteMessage => 'هل أنت متأكد من حذف هذا العنصر؟';
  @override
  String get cannotUndoAction => 'لا يمكن التراجع عن هذا الإجراء';

  // Premium
  @override
  String get premiumFeatures => 'الميزات المميزة';
  @override
  String get upgradeToPremium => 'الترقية للمميز';
  @override
  String get premiumBenefits => 'فوائد المميز';
  @override
  String get unlimitedVehicles => 'مركبات غير محدودة';
  @override
  String get advancedStatistics => 'إحصائيات متقدمة';
  @override
  String get cloudBackup => 'نسخ احتياطي سحابي';
  @override
  String get prioritySupport => 'دعم أولوية';
  @override
  String get adFree => 'بدون إعلانات';
  @override
  String get customThemes => 'مظاهر مخصصة';
  @override
  String get exportData => 'تصدير البيانات';
  @override
  String get premiumUser => 'مستخدم مميز';
  @override
  String get freeUser => 'مستخدم مجاني';
  @override
  String get purchasePremium => 'شراء المميز';
  @override
  String get restorePurchases => 'استعادة المشتريات';

  // Sauvegarde et restauration
  @override
  String get backupData => 'نسخ احتياطي للبيانات';
  @override
  String get restoreData => 'استعادة البيانات';
  @override
  String get exportToFile => 'تصدير إلى ملف';
  @override
  String get importFromFile => 'استيراد من ملف';
  @override
  String get selectFile => 'اختر ملف';
  @override
  String get backupCreated => 'تم إنشاء النسخة الاحتياطية';
  @override
  String get dataRestored => 'تم استعادة البيانات';
  @override
  String get backupFailed => 'فشل النسخ الاحتياطي';
  @override
  String get restoreFailed => 'فشلت الاستعادة';
  @override
  String get invalidBackupFile => 'ملف نسخ احتياطي غير صحيح';
  @override
  String get backupInProgress => 'النسخ الاحتياطي قيد التقدم';
  @override
  String get restoreInProgress => 'الاستعادة قيد التقدم';

  // Mise à jour
  @override
  String get updateAvailable => 'تحديث متاح';
  @override
  String get updateNow => 'تحديث الآن';
  @override
  String get updateLater => 'تحديث لاحقاً';
  @override
  String get newVersion => 'إصدار جديد';
  @override
  String get currentVersion => 'الإصدار الحالي';
  @override
  String get whatsNew => 'ما الجديد';
  @override
  String get updateFeatures => 'ميزات التحديث';
  @override
  String get downloadUpdate => 'تحميل التحديث';
  @override
  String get installUpdate => 'تثبيت التحديث';

  // Diagnostic
  @override
  String get diagnostic => 'التشخيص';
  @override
  String get runDiagnostic => 'تشغيل التشخيص';
  @override
  String get diagnosticResults => 'نتائج التشخيص';
  @override
  String get systemHealth => 'صحة النظام';
  @override
  String get databaseIntegrity => 'سلامة قاعدة البيانات';
  @override
  String get performanceCheck => 'فحص الأداء';
  @override
  String get storageUsage => 'استخدام التخزين';
  @override
  String get cacheSize => 'حجم التخزين المؤقت';
  @override
  String get clearCache => 'مسح التخزين المؤقت';
  @override
  String get optimizeDatabase => 'تحسين قاعدة البيانات';
  @override
  String get repairDatabase => 'إصلاح قاعدة البيانات';

  // Unités et formats
  @override
  String get kilometers => 'كيلومترات';
  @override
  String get km => 'كم';
  @override
  String get days => 'أيام';
  @override
  String get months => 'أشهر';
  @override
  String get years => 'سنوات';
  @override
  String get date => 'التاريخ';
  @override
  String get time => 'الوقت';
  @override
  String get percentage => 'النسبة المئوية';
  @override
  String get total => 'المجموع';
  @override
  String get average => 'المتوسط';
  @override
  String get minimum => 'الحد الأدنى';
  @override
  String get maximum => 'الحد الأقصى';

  // Actions communes
  @override
  String get sort => 'ترتيب';
  @override
  String get sortBy => 'ترتيب حسب';
  @override
  String get ascending => 'تصاعدي';
  @override
  String get descending => 'تنازلي';
  @override
  String get refresh => 'تحديث';
  @override
  String get reload => 'إعادة تحميل';
  @override
  String get reset => 'إعادة تعيين';
  @override
  String get clear => 'مسح';
  @override
  String get apply => 'تطبيق';
  @override
  String get retry => 'إعادة المحاولة';
  @override
  String get undo => 'تراجع';
  @override
  String get redo => 'إعادة';
  @override
  String get copy => 'نسخ';
  @override
  String get paste => 'لصق';
  @override
  String get share => 'مشاركة';
  @override
  String get print => 'طباعة';
  @override
  String get preview => 'معاينة';
  @override
  String get fullScreen => 'شاشة كاملة';
  @override
  String get minimize => 'تصغير';
  @override
  String get maximize => 'تكبير';

  // Messages contextuels
  @override
  String get noDataAvailable => 'لا توجد بيانات متاحة';
  @override
  String get noItemsFound => 'لم يتم العثور على عناصر';
  @override
  String get noResultsFound => 'لم يتم العثور على نتائج';
  @override
  String get emptyList => 'قائمة فارغة';
  @override
  String get addFirstItem => 'إضافة العنصر الأول';
  @override
  String get getStarted => 'ابدأ';
  @override
  String get welcomeMessage => 'مرحباً بك في كاروستي';
  @override
  String get tutorialTitle => 'الدليل التعليمي';
  @override
  String get skipTutorial => 'تخطي الدليل';
  @override
  String get nextStep => 'الخطوة التالية';
  @override
  String get previousStep => 'الخطوة السابقة';
  @override
  String get finish => 'إنهاء';
  @override
  String get congratulations => 'تهانينا';
  @override
  String get allDone => 'تم الانتهاء';

  // Textes manquants pour la traduction complète
  @override
  String get dashboard => 'لوحة التحكم';
  @override
  String get noVehicleConfigured => 'لا توجد مركبة مكونة';
  @override
  String get current => 'الحالي';
  @override
  String get remainingKilometers => 'الكيلومترات المتبقية';
  @override
  String get wear => 'التآكل';
  @override
  String get noMaintenanceNeeded => 'لا توجد صيانة وشيكة';
  @override
  String get expired => 'منتهي الصلاحية';
  @override
  String get valid => 'صالح';
  @override
  String get planned => 'مخطط';
  @override
  String get daysRemaining => 'متبقي';
  @override
  String get dateNotDefined => 'التاريخ غير محدد';
  @override
  String get mileage => 'المسافة المقطوعة';
  @override
  String get today => 'اليوم';
  @override
  String get yesterday => 'أمس';
  @override
  String get daysAgo => 'منذ';

  // About Screen
  @override
  String get description => 'الوصف';
  @override
  String get appDescription => 'كاروستي هو مساعدك الذكي لصيانة السيارات:\n\n'
      '• تتبع دقيق للمسافة المقطوعة\n'
      '• مسح تلقائي للوحة القيادة\n'
      '• تذكيرات ذكية للصيانة\n'
      '• تاريخ مفصل للصيانة\n'
      '• فترات قابلة للتخصيص\n'
      '• واجهة حديثة وبديهية';
  @override
  String get features => 'الميزات';
  @override
  String get mileageTracking => 'تتبع المسافة المقطوعة';
  @override
  String get mileageTrackingDesc => 'إدخال يدوي أو مسح تلقائي';
  @override
  String get maintenanceManagement => 'إدارة الصيانة';
  @override
  String get maintenanceManagementDesc => 'تغيير الزيت، الفلاتر، الفرامل، الإطارات، إلخ.';
  @override
  String get ocrScan => 'مسح OCR';
  @override
  String get ocrScanDesc => 'قراءة تلقائية للوحة القيادة';
  @override
  String get notificationsFeature => 'الإشعارات';
  @override
  String get notificationsFeatureDesc => 'تذكيرات ذكية للصيانة';
  @override
  String get completeHistory => 'التاريخ الكامل';
  @override
  String get completeHistoryDesc => 'تتبع جميع أعمال الصيانة';
  @override
  String get support => 'الدعم';
  @override
  String get supportDescription => 'لأي سؤال أو اقتراح أو مشكلة تقنية، لا تتردد في التواصل معنا.';
  @override
  String get contactEmail => 'البريد الإلكتروني للتواصل';
  @override
  String get copyEmail => 'نسخ البريد الإلكتروني';
  @override
  String get emailCopied => 'تم نسخ البريد الإلكتروني! 📧';
  @override
  String get visitWebsite => 'زيارة الموقع';
  @override
  String get cannotOpenWebsite => 'لا يمكن فتح الموقع الإلكتروني';
  @override
  String get errorOpening => 'خطأ أثناء الفتح';

  // Camera Scan Screen
  @override
  String get enterMileage => 'إدخال المسافة المقطوعة';
  @override
  String get scanMileage => 'مسح المسافة المقطوعة';
  @override
  String get autoScanUnavailable => 'المسح التلقائي غير متاح مؤقتاً';
  @override
  String get autoScanUnavailableDesc => 'ستكون ميزة المسح التلقائي للوحة القيادة متاحة في إصدار قادم من التطبيق.';
  @override
  String get processing => 'جاري المعالجة...';
  @override
  String get manualEntry => 'إدخال يدوي';
  @override
  String get enterValidMileage => 'يرجى إدخال مسافة مقطوعة صحيحة';

  // Help FAQ Screen
  @override
  String get helpFaq => 'المساعدة والأسئلة الشائعة';
  @override
  String get maintenances => 'الصيانة';
  @override
  String get data => 'البيانات';
  @override
  String get discoverApp => 'اكتشف التطبيق';
  @override
  String get discoverAppDesc => 'شاهد عرضنا التفاعلي لاكتشاف جميع الميزات';
  @override
  String get viewPresentation => 'مشاهدة العرض';
  @override
  String get quickGuide => 'دليل سريع';
  @override
  String get followMaintenances => 'متابعة الصيانة';
  @override
  String get consultStats => 'استشارة الإحصائيات';
  @override
  String get noQuestionFound => 'لم يتم العثور على أسئلة';

  // FAQ Questions and Answers
  @override
  String get faqAddVehicleQ => 'كيفية إضافة مركبة؟';
  @override
  String get faqAddVehicleA => 'اذهب إلى الإعدادات > إدارة المركبات > إضافة مركبة. أدخل معلومات مركبتك (العلامة التجارية، الطراز، السنة، المسافة المقطوعة الحالية).';
  @override
  String get faqPerformMaintenanceQ => 'كيفية تنفيذ صيانة؟';
  @override
  String get faqPerformMaintenanceA => 'من الشاشة الرئيسية، اضغط على عنصر الصيانة، ثم على "تنفيذ الصيانة". يمكنك أيضاً الذهاب إلى التشخيص > اختيار الصيانة.';
  @override
  String get faqNotificationsQ => 'لماذا لا أتلقى إشعارات؟';
  @override
  String get faqNotificationsA => 'تحقق من تفعيل الإشعارات في إعدادات هاتفك. تظهر الإشعارات عندما تقترب الصيانة من موعد استحقاقها.';
  @override
  String get faqCustomIntervalsQ => 'كيفية تخصيص الفترات؟';
  @override
  String get faqCustomIntervalsA => 'اذهب إلى الفترات > اختر الصيانة > عدّل الفترة المخصصة. يمكنك تكييف الفترات حسب عادات القيادة الخاصة بك.';
  @override
  String get faqAddCustomMaintenanceQ => 'كيفية إضافة صيانة مخصصة؟';
  @override
  String get faqAddCustomMaintenanceA => 'في الإحصائيات، اضغط على زر "+" ثم اختر "أخرى". أدخل اسم العملية والتكلفة والمسافة المقطوعة.';
  @override
  String get faqBackupDataQ => 'كيفية نسخ بياناتي احتياطياً؟';
  @override
  String get faqBackupDataA => 'اذهب إلى الإعدادات > النسخ الاحتياطي والاستعادة > تصدير البيانات. يمكنك استيراد وتصدير بياناتك في أي وقت.';
  @override
  String get faqChangeCurrencyQ => 'كيفية تغيير العملة؟';
  @override
  String get faqChangeCurrencyA => 'اذهب إلى الإعدادات > العملة لاختيار عملتك المحلية. ستظهر جميع التكاليف بهذه العملة.';
  @override
  String get faqWearPercentageQ => 'ما معنى نسبة التآكل؟';
  @override
  String get faqWearPercentageA => 'تشير نسبة التآكل إلى حالة قطعك بناءً على المسافة المقطوعة. 100% = صيانة ضرورية، 80% = ضرورية قريباً.';

  // Maintenance Detail Screen
  @override
  String get informations => 'المعلومات';
  @override
  String get reloadHistoryComment => 'إعادة تحميل التاريخ إذا تم تنفيذ الصيانة';

  // Engine categories
  @override
  String get braking => 'الفرامل';
  @override
  String get cooling => 'التبريد';
  @override
  String get comfort => 'الراحة';

  // Perform Maintenance Screen
  @override
  String get maintenanceInfoComment => 'معلومات حول عنصر الصيانة';
  @override
  String get details => 'التفاصيل';
  @override
  String get maintenanceKmLabel => 'المسافة المقطوعة للصيانة';
  @override
  String get maintenanceKmHint => 'كم أثناء الصيانة';
  @override
  String get enterKmError => 'يرجى إدخال المسافة المقطوعة';
  @override
  String get invalidKmError => 'مسافة مقطوعة غير صحيحة';
  @override
  String get garage => 'الكراج';
  @override
  String get observations => 'الملاحظات';
  @override
  String get maintenanceHelpText => 'تاريخ الصيانة';
  @override
  String get maintenanceSaved => 'تم حفظ الصيانة';

  // Maintenance Intervals Screen
  @override
  String get intervals => 'الفترات';
  @override
  String get extinguisher => 'مطفأة الحريق';
  @override
  String get extinguisherControl => 'فحص مطفأة الحريق';
  @override
  String get security => 'الأمان';
  @override
  String get configIntervals => 'إعداد الفترات';
  @override
  String get restoreDefault => 'استعادة افتراضي';
  @override
  String get custom => 'مخصص';
  @override
  String get defaultValue => 'افتراضي';
  @override
  String get modify => 'تعديل';
  @override
  String get modifyInterval => 'تعديل الفترة';
  @override
  String get enterValidInterval => 'يرجى إدخال فترة صحيحة';
  @override
  String get intervalUpdated => 'تم تحديث الفترة';
  @override
  String get intervalRestored => 'تم استعادة الفترة';
  @override
  String get restoreDefaultValues => 'استعادة القيم الافتراضية';
  @override
  String get featureInDevelopment => 'الميزة قيد التطوير';
  @override
  String get on => 'تشغيل';
  @override
  String get off => 'إيقاف';
  @override
  String get activate => 'تفعيل';
  @override
  String get deactivate => 'إلغاء تفعيل';

  // Maintenance Items Names
  @override
  String get oilChangeEngine => 'تغيير زيت المحرك';
  @override
  String get oilChangeEngineDesc => 'تغيير زيت المحرك والفلتر';
  @override
  String get fuelFilter => 'فلتر الوقود';
  @override
  String get fuelFilterDesc => 'استبدال فلتر الوقود';
  @override
  String get airFilter => 'فلتر الهواء';
  @override
  String get airFilterDesc => 'استبدال فلتر هواء المحرك';
  @override
  String get cabinFilter => 'فلتر المكيف';
  @override
  String get cabinFilterDesc => 'استبدال فلتر التكييف';
  @override
  String get timingBelt => 'سير التوقيت';
  @override
  String get timingBeltDesc => 'فحص واستبدال سير التوقيت';
  @override
  String get coolantFluid => 'سائل التبريد';
  @override
  String get coolantFluidDesc => 'تفريغ نظام التبريد';
  @override
  String get brakeFluid => 'سائل الفرامل';
  @override
  String get brakeFluidDesc => 'تفريغ سائل الفرامل';
  @override
  String get brakePads => 'أقراص الفرامل';
  @override
  String get brakePadsDesc => 'فحص واستبدال الأقراص';
  @override
  String get tiresItem => 'الإطارات';
  @override
  String get tiresItemDesc => 'فحص التآكل والدوران';

  // Document Items Names
  @override
  String get insuranceDoc => 'التأمين';
  @override
  String get insuranceDocDesc => 'تأمين السيارة الإجباري';
  @override
  String get technicalControlDoc => 'الفحص التقني';
  @override
  String get technicalControlDocDesc => 'الفحص التقني الدوري';
  @override
  String get extinguisherDoc => 'مطفأة الحريق';
  @override
  String get extinguisherDocDesc => 'فحص مطفأة الحريق';

  // Categories Names
  @override
  String get engineCategory => 'المحرك';
  @override
  String get filtersCategory => 'الفلاتر';
  @override
  String get brakingCategory => 'الفرامل';
  @override
  String get tiresCategory => 'الإطارات';
  @override
  String get coolingCategory => 'التبريد';
  @override
  String get comfortCategory => 'الراحة';
  @override
  String get insuranceCategory => 'التأمين';
  @override
  String get controlCategory => 'الفحص';
  @override
  String get securityCategory => 'الأمان';

  // Home Screen
  @override
  String get appName => 'كاروستي';
  @override
  String get toSchedule => 'للجدولة';

  // Maintenance Diagnostic Screen
  @override
  String get recreateDatabase => 'إعادة إنشاء القاعدة';
  @override
  String get noVehicle => 'لا توجد مركبة';
  @override
  String get noMaintenanceRecorded => 'لا توجد صيانة مسجلة';
  @override
  String get modifyKm => 'تعديل كم';
  @override
  String get kmUpdated => 'تم تحديث الكيلومترات';
  @override
  String get comparison => 'مقارنة';
  @override
  String get comparisonKm => 'مقارنة الكيلومترات';
  @override
  String get difference => 'الفرق';
  @override
  String get expiration => 'انتهاء الصلاحية';
  @override
  String get remains => 'متبقي';
  @override
  String get company => 'الشركة';
  @override
  String get selectDate => 'اختيار تاريخ';

  // Settings Screen
  @override
  String get configVehicle => 'إعداد المركبة';
  @override
  String get myVehicles => 'مركباتي';
  @override
  String get vehiclesConfigured => 'مركبة مُعدة';
  @override
  String get defaultAndCustomIntervals => 'الفترات الافتراضية والمخصصة';
  @override
  String get notificationReminders => 'تذكيرات الصيانة والتوقعات';
  @override
  String get application => 'التطبيق';
  @override
  String get colorsAndAppearance => 'الألوان والمظهر';
  @override
  String get appInfo => 'معلومات التطبيق';
  @override
  String get exportSuccess => 'تم التصدير بنجاح!';
  @override
  String get exportError => 'خطأ أثناء التصدير';
  @override
  String get closeLoadingIndicator => 'إغلاق مؤشر التحميل في حالة الخطأ';
  @override
  String get redirectToHome => 'إعادة التوجيه إلى الصفحة الرئيسية';
  @override
  String get availableLanguages => 'اللغات المتاحة';
  @override
  String get backupAndRestore => 'النسخ الاحتياطي والاستعادة';
  @override
  String get guideAndFaq => 'الدليل والأسئلة الشائعة';

  // Vehicle Config Screen
  @override
  String get brandHint => 'بيجو';
  @override
  String get engineHint => 'بنزين';
  @override
  String get kmHint => '50000';
  @override
  String get kmSuffix => 'كم';
  @override
  String get vehicleModified => 'تم تعديل المركبة';
  @override
  String get vehicleAdded => 'تم إضافة المركبة';
  @override
  String get noCurrentVehicle => 'لم يتم العثور على مركبة حالية';
  @override
  String get myVehicle => 'مركبتي';
  @override
  String get vehicleDeletedDefaultCreated => 'تم حذف المركبة وإنشاء مركبة افتراضية';

  // Statistics Screen
  @override
  String get analysisPeriod => 'فترة التحليل';
  @override
  String get oneYear => 'سنة واحدة';
  @override
  String get everything => 'الكل';
  @override
  String get distribution => 'التوزيع';
  @override
  String get maintenanceCosts => 'تكاليف الصيانة';
  @override
  String get thisMonth => 'هذا الشهر';
  @override
  String get thisYear => 'هذا العام';
  @override
  String get noMaintenanceCostRecorded => 'لا توجد تكاليف صيانة مسجلة';
  @override
  String get distributionByMaintenance => 'التوزيع حسب الصيانة';
  @override
  String get predictions => 'التوقعات';
  @override
  String get nextMaintenances => 'الصيانات القادمة';
  @override
  String get now => 'الآن';
  @override
  String get tomorrow => 'غداً';
  @override
  String get inDays => 'خلال {} يوم';
  @override
  String get inWeeks => 'خلال {} أسبوع';
  @override
  String get weeksAgo => 'منذ {} أسبوع';
  @override
  String get monthsAgo => 'منذ {} شهر';
  @override
  String get yearsAgo => 'منذ {} سنة';
  @override
  String get maintenanceType => 'نوع الصيانة';
  @override
  String get enterOperation => 'إدخال العملية';
  @override
  String get addToHistory => 'إضافة إلى السجل';
  @override
  String get modifyMaintenance => 'تعديل الصيانة';
  @override
  String get confirmDeletion => 'تأكيد الحذف';

  // Perform Maintenance Screen
  @override
  String get maintenanceDetails => 'التفاصيل';
  @override
  String get enterMileageValidation => 'يرجى إدخال المسافة المقطوعة';
  @override
  String get invalidMileage => 'مسافة غير صحيحة';
  @override
  String get maintenanceRecorded => 'تم تسجيل الصيانة';

  // Maintenance Detail Screen
  @override
  String get exceeded => 'تجاوز بـ';

  // Additional Maintenance Diagnostic
  @override
  String get recreateConfirm => 'تأكيد إعادة إنشاء قاعدة البيانات؟';
  @override
  String get databaseRecreated => 'تم إعادة إنشاء قاعدة البيانات';

  // Additional Statistics Screen
  @override
  String get operation => 'العملية';

  // Help FAQ Screen - Additional
  @override
  String get configureVehicleStep => 'إعداد المركبة';
  @override
  String get updateKmStep => 'تحديث الكيلومترات';
  @override
  String get followMaintenanceStep => 'متابعة الصيانة';
  @override
  String get consultStatsStep => 'مراجعة الإحصائيات';
  @override
  String get addNewVehicleQuestion => 'كيف أضيف مركبة جديدة؟';
  @override
  String get addNewVehicleAnswer => 'اذهب إلى الإعدادات > إدارة المركبات > إضافة مركبة. أدخل معلومات مركبتك (الماركة، الموديل، السنة، الكيلومترات الحالية).';
  @override
  String get scheduleMaintenanceQuestion => 'كيف أجدول الصيانة؟';
  @override
  String get scheduleMaintenanceAnswer => 'من الشاشة الرئيسية، اضغط على عنصر الصيانة، ثم على "تنفيذ الصيانة". يمكنك أيضاً الذهاب إلى التشخيص > اختيار الصيانة.';
  @override
  String get noNotificationsQuestion => 'لماذا لا أتلقى إشعارات؟';
  @override
  String get noNotificationsAnswer => 'تأكد من تفعيل الإشعارات في إعدادات هاتفك. تظهر الإشعارات عندما تقترب الصيانة من موعدها.';
  @override
  String get modifyIntervalsQuestion => 'كيف أعدل فترات الصيانة؟';
  @override
  String get modifyIntervalsAnswer => 'اذهب إلى الفترات > اختر الصيانة > عدل الفترة المخصصة. يمكنك تكييف الفترات حسب عادات القيادة.';
  @override
  String get addCustomOperationQuestion => 'كيف أضيف عملية مخصصة؟';
  @override
  String get addCustomOperationAnswer => 'في الإحصائيات، اضغط على زر "+" ثم اختر "أخرى". أدخل اسم العملية والتكلفة والكيلومترات.';
  @override
  String get exportDataQuestion => 'كيف أصدر بياناتي؟';
  @override
  String get exportDataAnswer => 'اذهب إلى الإعدادات > النسخ الاحتياطي والاستعادة > تصدير البيانات. يمكنك استيراد وتصدير بياناتك في أي وقت.';
  @override
  String get changeCurrencyQuestion => 'كيف أغير العملة؟';
  @override
  String get changeCurrencyAnswer => 'اذهب إلى الإعدادات > العملة لاختيار عملتك المحلية. ستظهر جميع التكاليف بهذه العملة.';
  @override
  String get wearPercentageQuestion => 'ما معنى نسب التآكل؟';
  @override
  String get wearPercentageAnswer => 'نسبة التآكل تشير إلى حالة قطعك بناءً على الكيلومترات. 100% = صيانة مطلوبة، 80% = مطلوبة قريباً.';

  // Privacy Policy Screen
  @override
  String get privacyPolicyTitle => 'سياسة الخصوصية';
  @override
  String get dataCollection => 'جمع البيانات';
  @override
  String get dataCollectionText => 'يجمع كاروستي فقط البيانات اللازمة لتشغيل التطبيق: معلومات مركبتك، تاريخ الصيانة وتفضيلات المستخدم. جميع البيانات مخزنة محلياً على جهازك.';
  @override
  String get dataUsage => 'استخدام البيانات';
  @override
  String get dataUsageText => 'تُستخدم بياناتك حصرياً لتوفير خدمات التطبيق: متابعة الصيانة، التذكيرات، الإحصائيات. لا يتم نقل أي بيانات لأطراف ثالثة دون موافقتك الصريحة.';
  @override
  String get dataSharing => 'مشاركة البيانات';
  @override
  String get dataSharingText => 'كاروستي لا يشارك أو يبيع أو يؤجر بياناتك الشخصية لأطراف ثالثة. معلوماتك تبقى خاصة وآمنة على جهازك.';
  @override
  String get dataSecurity => 'أمان البيانات';
  @override
  String get dataSecurityText => 'نطبق تدابير أمنية مناسبة لحماية بياناتك من الوصول غير المصرح به أو التعديل أو الحذف. التخزين المحلي يضمن تحكماً كاملاً في بياناتك.';
  @override
  String get userRights => 'حقوقك';
  @override
  String get userRightsText => 'لديك الحق في الوصول إلى بياناتك أو تعديلها أو حذفها في أي وقت عبر إعدادات التطبيق. يمكنك أيضاً تصدير بياناتك أو إعادة تعيين التطبيق.';
  @override
  String get contactUs => 'اتصل بنا';
  @override
  String get contactUsText => 'لأي استفسار حول سياسة الخصوصية هذه، اتصل بنا على: <EMAIL>';
  @override
  String get lastUpdated => 'آخر تحديث: يناير 2025';
  @override
  String get viewFullPolicy => 'عرض السياسة الكاملة';
  @override
  String get cannotOpenLink => 'لا يمكن فتح الرابط';
  @override
  String get errorOpeningLink => 'خطأ في فتح الرابط';
  @override
  String get privacyImportant => 'خصوصيتك مهمة';
  @override
  String get privacyDescription => 'كاروستي يحترم ويحمي بياناتك الشخصية.\nاكتشف كيف نضمن خصوصيتك.';
  @override
  String get allRightsReserved => '© 2025 كاروستي - جميع الحقوق محفوظة';

  // Presentation Screen
  @override
  String get skip => 'تخطي';
  @override
  String get presentationWelcomeTitle => 'مرحباً بك في كاروستي أوتو';
  @override
  String get presentationWelcomeDesc => 'أدر صيانة مركبتك بسهولة مع تطبيقنا الشامل والبديهي.';
  @override
  String get presentationMaintenanceTitle => 'متابعة الصيانة';
  @override
  String get presentationMaintenanceDesc => 'تابع جميع أعمال الصيانة: تغيير الزيت، الفلاتر، الفرامل، الإطارات وأكثر من ذلك. خصص الفترات حسب احتياجاتك.';
  @override
  String get presentationNotificationsTitle => 'إشعارات ذكية';
  @override
  String get presentationNotificationsDesc => 'احصل على تذكيرات تلقائية بناءً على المسافة المقطوعة وتآكل القطع. لن تفوت أي صيانة مهمة بعد الآن.';
  @override
  String get presentationHistoryTitle => 'تاريخ شامل';
  @override
  String get presentationHistoryDesc => 'اطلع على التاريخ المفصل لجميع أعمال الصيانة مع التكاليف والتواريخ والملاحظات. صدّر بياناتك بسهولة.';
  @override
  String get presentationStatsTitle => 'إحصائيات متقدمة';
  @override
  String get presentationStatsDesc => 'حلل تكاليف الصيانة، اعرض الاتجاهات وحسّن ميزانية السيارة مع الرسوم البيانية المفصلة.';
  @override
  String get presentationDiagnosticTitle => 'تشخيص ذكي';
  @override
  String get presentationDiagnosticDesc => 'استخدم نظام التشخيص لتقييم حالة مركبتك وتخطيط أعمال الصيانة ذات الأولوية.';
  @override
  String get presentationReadyTitle => 'جاهز للبدء؟';
  @override
  String get presentationReadyDesc => 'ابدأ الآن في العناية بمركبتك وأطل عمرها الافتراضي!';

  // Vehicle Config Screen - Additional
  @override
  String get modelHint => '308';
  @override
  String get yearHint => '2020';
  @override
  String get licensePlateHint => 'أ ب-123';
  @override
  String get deleteAction => 'حذف';
  @override
  String get cannotDeleteLastVehicle => 'لا يمكن حذف المركبة الأخيرة';
  @override
  String get vehicleSelected => 'تم اختيار المركبة';
  @override
  String get errorMessage => 'خطأ';

  // Import Export Messages
  @override
  String get fileSelected => 'تم اختيار الملف';
  @override
  String get exportInProgress => 'جاري التصدير...';
  @override
  String get exportComplete => 'اكتمل التصدير';
  @override
  String get restoreComplete => 'اكتملت الاستعادة';
  @override
  String get invalidFileFormat => 'تنسيق ملف غير صحيح';
  @override
  String get fileNotFound => 'الملف غير موجود';
  @override
  String get permissionDenied => 'تم رفض الإذن';
  @override
  String get storageAccessRequired => 'مطلوب الوصول للتخزين';
  @override
  String get chooseBackupFile => 'اختر ملف النسخة الاحتياطية';
  @override
  String get createBackup => 'إنشاء نسخة احتياطية';
  @override
  String get restoreFromBackup => 'استعادة من نسخة احتياطية';
  @override
  String get dataWillBeReplaced => 'سيتم استبدال جميع البيانات الحالية';
  @override
  String get confirmRestore => 'تأكيد الاستعادة';
  @override
  String get backupSavedTo => 'تم حفظ النسخة الاحتياطية في';

  // App Update Messages
  @override
  String get newVersionAvailable => 'إصدار جديد متاح';
  @override
  String get currentVersionText => 'الإصدار الحالي';
  @override
  String get latestVersionText => 'أحدث إصدار';
  @override
  String get improvements => 'تحسينات';
  @override
  String get downloadingUpdate => 'جاري تحميل التحديث...';
  @override
  String get updateDownloaded => 'تم تحميل التحديث';
  @override
  String get updateFailed => 'فشل التحديث';
  @override
  String get appUpToDate => 'التطبيق محدث';
  @override
  String get updateRequired => 'تحديث مطلوب';
  @override
  String get criticalUpdate => 'تحديث حرج';
  @override
  String get skipThisVersion => 'تخطي هذا الإصدار';

  // App Rating Messages
  @override
  String get rateAppTitle => 'تقييم كاروستي';
  @override
  String get rateAppMessage => 'هل تحب استخدام كاروستي؟ رأيك يساعدنا على تحسين التطبيق!';
  @override
  String get rateAppPositive => 'أحبه! ⭐⭐⭐⭐⭐';
  @override
  String get rateAppNegative => 'يمكن أن يكون أفضل';
  @override
  String get rateAppNeutral => 'مقبول';
  @override
  String get enjoyingApp => 'هل تستمتع بكاروستي؟';
  @override
  String get wouldYouRate => 'هل يمكنك تقييمنا؟';
  @override
  String get feedbackTitle => 'تعليقاتك';
  @override
  String get feedbackMessage => 'أخبرنا كيف يمكننا تحسين كاروستي لك!';
  @override
  String get sendFeedback => 'إرسال التعليقات';
  @override
  String get thankYouRating => 'شكراً لتقييمك!';
  @override
  String get rateOnStore => 'تقييم في المتجر';
  @override
  String get maybeLater => 'ربما لاحقاً';
  @override
  String get noThanks => 'لا شكراً';
  @override
  String get howCanImprove => 'كيف يمكننا التحسن؟';
  @override
  String get tellUsMore => 'أخبرنا المزيد...';
  @override
  String get submitFeedback => 'إرسال الملاحظات';
  @override
  String get feedbackSent => 'تم إرسال الملاحظات! شكراً!';
  @override
  String get openStore => 'فتح المتجر';

  // Additional Help FAQ
  @override
  String get parameters => 'المعاملات';

  // Import Export Additional
  @override
  String get importData => 'استيراد';
}