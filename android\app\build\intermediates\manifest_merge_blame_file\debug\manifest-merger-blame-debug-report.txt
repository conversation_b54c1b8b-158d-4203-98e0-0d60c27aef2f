1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.carosti.app"
4    android:versionCode="2"
5    android:versionName="1.1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:4:5-67
15-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:4:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:5:5-79
16-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:5:22-76
17    <!--
18 Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:50:5-55:15
25        <intent>
25-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:51:9-54:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:52:13-72
26-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:52:21-70
27
28            <data android:mimeType="text/plain" />
28-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:13-50
28-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:19-48
29        </intent>
30        <intent>
30-->[:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
31            <action android:name="android.intent.action.GET_CONTENT" />
31-->[:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
31-->[:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
32
33            <data android:mimeType="*/*" />
33-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:13-50
33-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:19-48
34        </intent> <!-- For browser content -->
35        <intent>
35-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:38:9-44:18
36            <action android:name="android.intent.action.VIEW" />
36-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:13-65
36-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:21-62
37
38            <category android:name="android.intent.category.BROWSABLE" />
38-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:41:13-74
38-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:41:23-71
39
40            <data android:scheme="https" />
40-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:13-50
41        </intent> <!-- End of browser content -->
42        <!-- For CustomTabsService -->
43        <intent>
43-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
44            <action android:name="android.support.customtabs.action.CustomTabsService" />
44-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
44-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
45        </intent> <!-- End of CustomTabsService -->
46        <!-- For MRAID capabilities -->
47        <intent>
47-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
48            <action android:name="android.intent.action.INSERT" />
48-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
48-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
49
50            <data android:mimeType="vnd.android.cursor.dir/event" />
50-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:13-50
50-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:19-48
51        </intent>
52        <intent>
52-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
53            <action android:name="android.intent.action.VIEW" />
53-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:13-65
53-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:21-62
54
55            <data android:scheme="sms" />
55-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:13-50
56        </intent>
57        <intent>
57-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
58            <action android:name="android.intent.action.DIAL" />
58-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
58-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
59
60            <data android:path="tel:" />
60-->C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:13-50
61        </intent>
62    </queries>
63
64    <uses-permission
64-->[:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:38
65        android:name="android.permission.READ_EXTERNAL_STORAGE"
65-->[:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-64
66        android:maxSdkVersion="32" />
66-->[:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-35
67    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
67-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:5-79
67-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:22-76
68    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
68-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
68-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
69    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
69-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
69-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
70    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
70-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
70-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
71    <uses-permission android:name="android.permission.WAKE_LOCK" />
71-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
71-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
72    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
72-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
72-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
73
74    <permission
74-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
75        android:name="com.carosti.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
75-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
76        android:protectionLevel="signature" />
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
77
78    <uses-permission android:name="com.carosti.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
78-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
78-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
79
80    <application
81        android:name="android.app.Application"
82        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
82-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
83        android:debuggable="true"
84        android:extractNativeLibs="true"
85        android:icon="@mipmap/ic_launcher"
86        android:label="@string/app_name" >
87        <activity
88            android:name="com.carosti.app.MainActivity"
89            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
90            android:exported="true"
91            android:hardwareAccelerated="true"
92            android:launchMode="singleTop"
93            android:taskAffinity=""
94            android:theme="@style/LaunchTheme"
95            android:windowSoftInputMode="adjustResize" >
96
97            <!--
98                 Specifies an Android theme to apply to this Activity as soon as
99                 the Android process has started. This theme is visible to the user
100                 while the Flutter UI initializes. After that, this theme continues
101                 to determine the Window background behind the Flutter UI.
102            -->
103            <meta-data
104                android:name="io.flutter.embedding.android.NormalTheme"
105                android:resource="@style/NormalTheme" />
106
107            <intent-filter>
108                <action android:name="android.intent.action.MAIN" />
109
110                <category android:name="android.intent.category.LAUNCHER" />
111            </intent-filter>
112        </activity>
113
114        <!-- Google AdMob App ID -->
115        <meta-data
116            android:name="com.google.android.gms.ads.APPLICATION_ID"
117            android:value="ca-app-pub-4778309361021572~**********" />
118
119        <!--
120             Don't delete the meta-data below.
121             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
122        -->
123        <meta-data
124            android:name="flutterEmbedding"
125            android:value="2" />
126        <!--
127           Declares a provider which allows us to store files to share in
128           '.../caches/share_plus' and grant the receiving action access
129        -->
130        <provider
130-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
131            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
131-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
132            android:authorities="com.carosti.app.flutter.share_provider"
132-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
133            android:exported="false"
133-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
134            android:grantUriPermissions="true" >
134-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
135            <meta-data
135-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
136                android:name="android.support.FILE_PROVIDER_PATHS"
136-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
137                android:resource="@xml/flutter_share_file_paths" />
137-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
138        </provider>
139        <!--
140           This manifest declared broadcast receiver allows us to use an explicit
141           Intent when creating a PendingItent to be informed of the user's choice
142        -->
143        <receiver
143-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
144            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
144-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
145            android:exported="false" >
145-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
146            <intent-filter>
146-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
147                <action android:name="EXTRA_CHOSEN_COMPONENT" />
147-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
147-->[:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
148            </intent-filter>
149        </receiver>
150
151        <meta-data
151-->[:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:36
152            android:name="io.flutter.embedded_views_preview"
152-->[:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-61
153            android:value="true" />
153-->[:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-33
154
155        <activity
155-->[:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
156            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
156-->[:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
157            android:exported="false"
157-->[:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
158            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
158-->[:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
159
160        <uses-library
160-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
161            android:name="androidx.window.extensions"
161-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
162            android:required="false" />
162-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
163        <uses-library
163-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
164            android:name="androidx.window.sidecar"
164-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
165            android:required="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
165-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
166        <activity
166-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
167            android:name="com.google.android.gms.ads.AdActivity"
167-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
168            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
168-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
169            android:exported="false"
169-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
170            android:theme="@android:style/Theme.Translucent" />
170-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
171
172        <provider
172-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
173            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
173-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
174            android:authorities="com.carosti.app.mobileadsinitprovider"
174-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
175            android:exported="false"
175-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
176            android:initOrder="100" />
176-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
177
178        <service
178-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
179            android:name="com.google.android.gms.ads.AdService"
179-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
180            android:enabled="true"
180-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
181            android:exported="false" />
181-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
182
183        <activity
183-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
184            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
184-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
185            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
185-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
186            android:exported="false" />
186-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
187        <activity
187-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
188            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
188-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
189            android:excludeFromRecents="true"
189-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
190            android:exported="false"
190-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
191            android:launchMode="singleTask"
191-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
192            android:taskAffinity=""
192-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
193            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
193-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
194
195        <property
195-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:107:9-109:62
196            android:name="android.adservices.AD_SERVICES_CONFIG"
196-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:108:13-65
197            android:resource="@xml/gma_ad_services_config" />
197-->[com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:109:13-59
198
199        <activity
199-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
200            android:name="com.google.android.gms.common.api.GoogleApiActivity"
200-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:19-85
201            android:exported="false"
201-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:22:19-43
202            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
202-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:21:19-78
203
204        <meta-data
204-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
205            android:name="com.google.android.gms.version"
205-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
206            android:value="@integer/google_play_services_version" />
206-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
207
208        <uses-library
208-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
209            android:name="android.ext.adservices"
209-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
210            android:required="false" />
210-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
211
212        <provider
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
213            android:name="androidx.startup.InitializationProvider"
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
214            android:authorities="com.carosti.app.androidx-startup"
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
215            android:exported="false" >
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
216            <meta-data
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
217                android:name="androidx.work.WorkManagerInitializer"
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
218                android:value="androidx.startup" />
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
219            <meta-data
219-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
220                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
220-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
221                android:value="androidx.startup" />
221-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
222            <meta-data
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
223                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
224                android:value="androidx.startup" />
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
225        </provider>
226
227        <service
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
228            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
229            android:directBootAware="false"
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
230            android:enabled="@bool/enable_system_alarm_service_default"
230-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
231            android:exported="false" />
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
232        <service
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
233            android:name="androidx.work.impl.background.systemjob.SystemJobService"
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
234            android:directBootAware="false"
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
235            android:enabled="@bool/enable_system_job_service_default"
235-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
236            android:exported="true"
236-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
237            android:permission="android.permission.BIND_JOB_SERVICE" />
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
238        <service
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
239            android:name="androidx.work.impl.foreground.SystemForegroundService"
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
240            android:directBootAware="false"
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
241            android:enabled="@bool/enable_system_foreground_service_default"
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
242            android:exported="false" />
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
243
244        <receiver
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
245            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
246            android:directBootAware="false"
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
247            android:enabled="true"
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
248            android:exported="false" />
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
249        <receiver
249-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
250            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
250-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
251            android:directBootAware="false"
251-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
252            android:enabled="false"
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
253            android:exported="false" >
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
254            <intent-filter>
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
255                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
256                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
257            </intent-filter>
258        </receiver>
259        <receiver
259-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
260            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
260-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
262            android:enabled="false"
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
263            android:exported="false" >
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
264            <intent-filter>
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
265                <action android:name="android.intent.action.BATTERY_OKAY" />
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
266                <action android:name="android.intent.action.BATTERY_LOW" />
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
267            </intent-filter>
268        </receiver>
269        <receiver
269-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
270-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
272            android:enabled="false"
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
275                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
276                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
276-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
277            </intent-filter>
278        </receiver>
279        <receiver
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
280            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
281            android:directBootAware="false"
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
282            android:enabled="false"
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
283            android:exported="false" >
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
284            <intent-filter>
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
285                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
286            </intent-filter>
287        </receiver>
288        <receiver
288-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
289            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
290            android:directBootAware="false"
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
291            android:enabled="false"
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
292            android:exported="false" >
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
293            <intent-filter>
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
294                <action android:name="android.intent.action.BOOT_COMPLETED" />
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
295                <action android:name="android.intent.action.TIME_SET" />
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
296                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
296-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
297            </intent-filter>
298        </receiver>
299        <receiver
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
300            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
302            android:enabled="@bool/enable_system_alarm_service_default"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
303            android:exported="false" >
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
304            <intent-filter>
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
305                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
306            </intent-filter>
307        </receiver>
308        <receiver
308-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
309            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
309-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
310            android:directBootAware="false"
310-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
311            android:enabled="true"
311-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
312            android:exported="true"
312-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
313            android:permission="android.permission.DUMP" >
313-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
314            <intent-filter>
314-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
315                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
316            </intent-filter>
317        </receiver>
318        <receiver
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
319            android:name="androidx.profileinstaller.ProfileInstallReceiver"
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
320            android:directBootAware="false"
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
321            android:enabled="true"
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
322            android:exported="true"
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
323            android:permission="android.permission.DUMP" >
323-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
324            <intent-filter>
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
325                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
326            </intent-filter>
327            <intent-filter>
327-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
328                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
329            </intent-filter>
330            <intent-filter>
330-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
331                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
331-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
331-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
332            </intent-filter>
333            <intent-filter>
333-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
334                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
334-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
334-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
335            </intent-filter>
336        </receiver>
337
338        <service
338-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
339            android:name="androidx.room.MultiInstanceInvalidationService"
339-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
340            android:directBootAware="true"
340-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
341            android:exported="false" />
341-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
342    </application>
343
344</manifest>
