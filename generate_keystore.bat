@echo off
echo ========================================
echo   GENERATION KEYSTORE GOOGLE PLAY
echo ========================================
echo.

REM Vérifier si le certificat existe
if not exist "deployment_cert.der" (
    echo ❌ ERREUR: Le fichier deployment_cert.der n'existe pas
    echo    Assurez-vous qu'il est dans le dossier du projet
    pause
    exit /b 1
)

echo ✅ Certificat deployment_cert.der trouvé
echo.

REM Demander les informations à l'utilisateur
echo 📝 Configuration du nouveau keystore:
echo.
set /p KEYSTORE_NAME="Nom du keystore (défaut: google-play-release.jks): "
if "%KEYSTORE_NAME%"=="" set KEYSTORE_NAME=google-play-release.jks

set /p KEY_ALIAS="Alias de la clé (défaut: upload-key): "
if "%KEY_ALIAS%"=="" set KEY_ALIAS=upload-key

set /p STORE_PASSWORD="Mot de passe du keystore: "
if "%STORE_PASSWORD%"=="" (
    echo ❌ Le mot de passe du keystore est obligatoire
    pause
    exit /b 1
)

set /p KEY_PASSWORD="Mot de passe de la clé (Entrée = même que keystore): "
if "%KEY_PASSWORD%"=="" set KEY_PASSWORD=%STORE_PASSWORD%

echo.
echo 📋 Récapitulatif:
echo    Keystore: %KEYSTORE_NAME%
echo    Alias: %KEY_ALIAS%
echo    Mot de passe keystore: %STORE_PASSWORD%
echo    Mot de passe clé: %KEY_PASSWORD%
echo.
set /p CONFIRM="Confirmer la génération? (o/N): "
if /i not "%CONFIRM%"=="o" (
    echo ❌ Génération annulée
    pause
    exit /b 1
)

echo.
echo 🔄 Génération du keystore en cours...

REM Supprimer les anciens keystores s'ils existent
if exist "%KEYSTORE_NAME%" (
    echo 🗑️ Suppression de l'ancien keystore %KEYSTORE_NAME%...
    del "%KEYSTORE_NAME%"
)

if exist "google-play-compatible.jks" (
    echo 🗑️ Suppression de google-play-compatible.jks...
    del "google-play-compatible.jks"
)

if exist "android\carosti-release-key.jks" (
    echo 🗑️ Suppression de android\carosti-release-key.jks...
    del "android\carosti-release-key.jks"
)

REM Créer un nouveau keystore pour l'upload
echo 🔐 Génération d'une nouvelle clé d'upload...
"C:\Program Files\Android\Android Studio\jbr\bin\keytool" -genkeypair -alias %KEY_ALIAS% -keyalg RSA -keysize 2048 -validity 25000 -keystore %KEYSTORE_NAME% -storepass %STORE_PASSWORD% -keypass %KEY_PASSWORD% -dname "CN=MyCar Maintenance, OU=Carosti, O=Carosti App, L=Algiers, ST=Algiers, C=DZ"
if errorlevel 1 (
    echo ❌ ERREUR: Impossible de créer le keystore
    echo    Vérification avec keytool système...
    keytool -genkeypair -alias %KEY_ALIAS% -keyalg RSA -keysize 2048 -validity 25000 -keystore %KEYSTORE_NAME% -storepass %STORE_PASSWORD% -keypass %KEY_PASSWORD% -dname "CN=MyCar Maintenance, OU=Carosti, O=Carosti App, L=Algiers, ST=Algiers, C=DZ"
    if errorlevel 1 (
        echo ❌ ERREUR: Keytool non trouvé
        pause
        exit /b 1
    )
)

echo.
echo ✅ Keystore généré avec succès: %KEYSTORE_NAME%
echo.

REM Créer le fichier key.properties
echo 📝 Création du fichier key.properties...
(
echo # Configuration Google Play Store - Généré automatiquement
echo # Keystore pour upload sur Google Play
echo storePassword=%STORE_PASSWORD%
echo keyPassword=%KEY_PASSWORD%
echo keyAlias=%KEY_ALIAS%
echo storeFile=%KEYSTORE_NAME%
) > key.properties

echo ✅ Fichier key.properties créé
echo.

REM Créer le fichier key.properties pour Android
echo 📝 Création du fichier android/key.properties...
(
echo # Configuration Google Play Store - Généré automatiquement
echo # Keystore pour upload sur Google Play
echo storePassword=%STORE_PASSWORD%
echo keyPassword=%KEY_PASSWORD%
echo keyAlias=%KEY_ALIAS%
echo storeFile=../%KEYSTORE_NAME%
) > android/key.properties

echo ✅ Fichier android/key.properties créé
echo.

echo 🎉 CONFIGURATION TERMINÉE !
echo.
echo 📋 Résumé:
echo    ✅ Nouveau keystore: %KEYSTORE_NAME%
echo    ✅ Fichiers key.properties mis à jour
echo    ✅ Anciens keystores supprimés
echo.
echo 🚀 Vous pouvez maintenant construire votre AAB avec:
echo    flutter build appbundle --release
echo.
echo 📝 IMPORTANT: Gardez précieusement vos mots de passe !
echo.
pause
