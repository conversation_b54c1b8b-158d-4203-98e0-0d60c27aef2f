import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_item.dart';
import '../models/document_item.dart';
import '../services/theme_service.dart';
import '../services/update_service.dart';
import '../l10n/app_localizations.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';
import '../widgets/admob_home_banner.dart';
import '../widgets/modern_drawer.dart';
import '../services/translation_service.dart';
import 'settings_screen.dart';

import 'kilometer_input_screen.dart';
import 'vehicle_config_screen.dart';
import 'notifications_screen.dart';
import 'maintenance_diagnostic_screen.dart';
import 'statistics_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MaintenanceProvider>().initialize();
    });
    _checkForUpdates();
  }

  /// Vérifie les mises à jour disponibles
  Future<void> _checkForUpdates() async {
    // Attendre un peu pour que l'interface se charge
    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) return;

    try {
      final updateService = UpdateService();
      final hasUpdate = await updateService.checkForUpdate();

      if (hasUpdate && mounted) {
        await updateService.showUpdateDialog(context);
      }
    } catch (e) {
      debugPrint('❌ Erreur vérification mises à jour: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeService = context.watch<ThemeService>();
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      drawer: const ModernDrawer(),
      appBar: AppBar(
        title: Text(
          l10n.appTitle,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            letterSpacing: 1.0,
            color: Colors.white,
            fontSize: 16,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, size: 22),
            onPressed: () => _navigateToNotifications(context),
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined, size: 22),
            onPressed: () => _navigateToSettings(context),
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Container(
        decoration: themeService.createProfessionalBackground(),
        child: Consumer<MaintenanceProvider>(
          builder: (context, provider, child) {
            if (provider.isLoading) {
              return Container(
                color: Colors.white,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Image animée de chargement CAROSTI - agrandie et sans ombres
                      Container(
                        width: 160, // Agrandi de 120 à 160
                        height: 160, // Agrandi de 120 à 160
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          // Suppression des boxShadow pour éliminer les ombres
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: Image.asset(
                            'assets/images/Movedicon.gif',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              // Fallback si l'image ne charge pas
                              return Container(
                                decoration: BoxDecoration(
                                  color: themeService.primaryColor.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Icon(
                                  Icons.directions_car,
                                  size:
                                      80, // Agrandi de 60 à 80 pour correspondre
                                  color: themeService.primaryColor,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      // Nom de l'application
                      Text(
                        l10n.appName,
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: themeService.primaryColor,
                              letterSpacing: 2,
                            ),
                      ),
                      const SizedBox(height: 16),
                      // Texte de chargement
                      Text(
                        l10n.loading,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: ThemeService.professionalNeutrals['gray_600']!,
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Indicateur de progression moderne
                      Container(
                        width: 200,
                        height: 4,
                        decoration: BoxDecoration(
                          color: ThemeService.professionalNeutrals['gray_200']!,
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: LinearProgressIndicator(
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            themeService.primaryColor,
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }

            if (provider.error != null) {
              return Center(
                child: Container(
                  margin: const EdgeInsets.all(24),
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red[400],
                      ),
                      const SizedBox(height: 20),
                      Text(
                        l10n.error,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.w700),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        provider.error!,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: () {
                          provider.clearError();
                          provider.initialize();
                        },
                        child: Text(l10n.retry),
                      ),
                    ],
                  ),
                ),
              );
            }

            return SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Widget véhicule actuel (remplace l'en-tête)
                    _buildVehicleInfoCard(context, provider),

                    const SizedBox(height: 32),

                    // Statistiques professionnelles
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: themeService.createProfessionalShadow(),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            l10n.overview,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: ThemeService
                                      .professionalNeutrals['gray_900']!,
                                  fontSize: 14,
                                ),
                          ),
                          const SizedBox(height: 20),
                          Row(
                            children: [
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  l10n.total,
                                  _getActiveMaintenanceItemsCount(provider).toString(),
                                  l10n.maintenance.toLowerCase(),
                                  MdiIcons.wrench,
                                  ThemeService
                                      .professionalColors['corporate_blue']!,
                                  () => _navigateToTotalMaintenance(context),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  l10n.urgent,
                                  provider.itemsDue.length.toString(),
                                  l10n.toProcess,
                                  MdiIcons.alertCircle,
                                  ThemeService.professionalSemantics['error']!,
                                  () => _navigateToUrgentMaintenance(
                                    context,
                                    provider,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  l10n.upcoming,
                                  provider.itemsApproaching.length.toString(),
                                  l10n.toPlan,
                                  MdiIcons.clockAlert,
                                  ThemeService
                                      .professionalSemantics['warning']!,
                                  () => _navigateToApproachingMaintenance(
                                    context,
                                    provider,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildProfessionalStatCard(
                                  context,
                                  l10n.upToDate,
                                  (_getActiveMaintenanceItemsCount(provider) -
                                          provider.itemsDue.length -
                                          provider.itemsApproaching.length)
                                      .toString(),
                                  l10n.inOrder,
                                  MdiIcons.checkCircle,
                                  ThemeService
                                      .professionalSemantics['success']!,
                                  () => _navigateToOkMaintenance(
                                    context,
                                    provider,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Prochain entretien moderne
                    _buildModernNextMaintenanceSection(context, provider),

                    const SizedBox(height: 32),

                    // Section Documents (Assurance & Contrôle Technique)
                    _buildDocumentsSection(context, provider),

                    const SizedBox(height: 32),

                    // Actions rapides modernes
                    ModernFadeIn(
                      delay: const Duration(milliseconds: 300),
                      child: ModernCard(
                        padding: const EdgeInsets.all(24),
                        enableHoverEffect: true,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            l10n.quickActions,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: ThemeService
                                      .professionalNeutrals['gray_900']!,
                                  fontSize: 14,
                                ),
                          ),
                          const SizedBox(height: 20),
                          Row(
                            children: [
                              Expanded(
                                child: _buildProfessionalActionButton(
                                  context,
                                  l10n.configureVehicle,
                                  l10n.configure,
                                  MdiIcons.carCog,
                                  () => _navigateToVehicleConfig(context),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildProfessionalActionButton(
                                  context,
                                  l10n.statistics,
                                  l10n.analyze,
                                  MdiIcons.chartLine,
                                  () => _navigateToStatistics(context),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToKilometerInput(context),
        icon: Icon(MdiIcons.speedometer, size: 16),
        label: Text(
          l10n.km,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 11),
        ),
        backgroundColor: themeService.primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        extendedPadding: const EdgeInsets.symmetric(horizontal: 12),
      ),
      bottomNavigationBar: const AdMobHomeBottomBanner(),
    );
  }

  // Méthode pour les cartes de statistiques professionnelles
  Widget _buildProfessionalStatCard(
    BuildContext context,
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, color: color, size: 14),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: ThemeService.professionalNeutrals['gray_700']!,
                      fontSize: 11,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
                color: color,
                fontSize: 20,
              ),
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeService.professionalNeutrals['gray_500']!,
                fontWeight: FontWeight.w400,
                fontSize: 9,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthode pour les boutons d'action professionnels
  Widget _buildProfessionalActionButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ThemeService.professionalNeutrals['gray_50']!,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: ThemeService.professionalNeutrals['gray_200']!,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ThemeService.professionalColors['corporate_blue']!
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                icon,
                color: ThemeService.professionalColors['corporate_blue']!,
                size: 16,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w700,
                color: ThemeService.professionalNeutrals['gray_700']!,
                fontSize: 10,
              ),
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeService.professionalNeutrals['gray_500']!,
                fontWeight: FontWeight.w600,
                fontSize: 9,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Nouvelle section moderne pour le prochain entretien
  Widget _buildModernNextMaintenanceSection(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    final l10n = AppLocalizations.of(context);
    final stats = provider.getDetailedStatistics();
    final nextItem = stats['nextMaintenanceItem'] as MaintenanceItem?;
    final nextKm = stats['nextMaintenanceKm'] as int?;

    return ModernFadeIn(
      delay: const Duration(milliseconds: 100),
      child: ModernCard(
        padding: const EdgeInsets.all(16),
        enableHoverEffect: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                MdiIcons.clockAlert,
                color: ThemeService.professionalColors['corporate_blue']!,
                size: 18,
              ),
              const SizedBox(width: 12),
              Text(
                l10n.next,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeService.professionalNeutrals['gray_900']!,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          nextItem != null && nextKm != null
              ? _buildModernNextMaintenanceInfo(
                  context,
                  provider,
                  nextItem,
                  nextKm,
                )
              : _buildModernNoMaintenanceNeeded(context),
        ],
      ),
      ),
    );
  }

  Widget _buildModernNextMaintenanceInfo(
    BuildContext context,
    MaintenanceProvider provider,
    MaintenanceItem item,
    int remainingKm,
  ) {
    final l10n = AppLocalizations.of(context);
    final translationService = TranslationService();
    final wearPercentage = provider.calculateWearPercentage(item);

    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (wearPercentage >= 100) {
      statusColor = Colors.redAccent;
      statusIcon = MdiIcons.alertCircle;
      statusText = l10n.urgent;
    } else if (wearPercentage >= 80) {
      statusColor = Colors.orangeAccent;
      statusIcon = MdiIcons.clockAlert;
      statusText = l10n.soon;
    } else if (wearPercentage >= 60) {
      statusColor = Colors.blueAccent;
      statusIcon = MdiIcons.calendar;
      statusText = l10n.planned;
    } else {
      statusColor = Colors.greenAccent;
      statusIcon = MdiIcons.checkCircle;
      statusText = l10n.ok;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              MdiIcons.wrench,
              color: ThemeService.professionalColors['corporate_blue']!,
              size: 16,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                translationService.translateMaintenanceName(item.name, l10n),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeService.professionalNeutrals['gray_900']!,
                  fontSize: 12,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: statusColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(statusIcon, color: statusColor, size: 14),
                  const SizedBox(width: 6),
                  Text(
                    statusText,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 8,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: _buildModernInfoItem(
                context,
                l10n.remainingKilometers,
                '$remainingKm km',
                MdiIcons.speedometer,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernInfoItem(
                context,
                l10n.wear,
                '${wearPercentage.toInt()}%',
                MdiIcons.gauge,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModernNoMaintenanceNeeded(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      children: [
        Icon(
          MdiIcons.checkCircleOutline,
          size: 48,
          color: ThemeService.professionalSemantics['success']!,
        ),
        const SizedBox(height: 16),
        Text(
          l10n.upToDate,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: ThemeService.professionalNeutrals['gray_900']!,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          l10n.noMaintenanceNeeded,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: ThemeService.professionalNeutrals['gray_600']!,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildModernInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeService.professionalNeutrals['gray_50']!,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeService.professionalNeutrals['gray_200']!,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: ThemeService.professionalColors['corporate_blue']!,
            size: 18,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: ThemeService.professionalNeutrals['gray_900']!,
              fontSize: 14,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeService.professionalNeutrals['gray_600']!,
              fontSize: 9,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Section Documents (Assurance & Contrôle Technique)
  Widget _buildDocumentsSection(BuildContext context, MaintenanceProvider provider) {
    final l10n = AppLocalizations.of(context);
    final documents = provider.documentItems;

    print('🔍 Documents trouvés: ${documents.length}');
    for (final doc in documents) {
      print('📄 Document: ${doc.name} - Expire: ${doc.expirationDate}');
    }

    if (documents.isEmpty) {
      print('❌ Aucun document trouvé - section masquée');
      return const SizedBox.shrink();
    }

    return ModernFadeIn(
      delay: const Duration(milliseconds: 200),
      child: ModernCard(
        padding: const EdgeInsets.all(24),
        enableHoverEffect: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: ThemeService.professionalColors['corporate_blue']!.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  MdiIcons.fileDocument,
                  color: ThemeService.professionalColors['corporate_blue']!,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                l10n.documents,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeService.professionalNeutrals['gray_900']!,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...documents.map((document) => _buildDocumentCard(context, document)),
        ],
      ),
      ),
    );
  }

  Widget _buildDocumentCard(BuildContext context, DocumentItem document) {
    final l10n = AppLocalizations.of(context);
    final translationService = TranslationService();
    final daysRemaining = document.daysUntilExpiration;
    final isExpired = document.isExpired;
    final isUrgent = document.isUrgent;
    final isExpiringSoon = document.isExpiringSoon;

    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (isExpired) {
      statusColor = Colors.red;
      statusText = l10n.expired;
      statusIcon = MdiIcons.alertCircle;
    } else if (isUrgent) {
      statusColor = Colors.red;
      statusText = l10n.urgent;
      statusIcon = MdiIcons.alertCircle;
    } else if (daysRemaining <= 15) {
      statusColor = Colors.orange;
      statusText = l10n.soon;
      statusIcon = MdiIcons.clockAlert;
    } else if (isExpiringSoon) {
      statusColor = Colors.blue;
      statusText = l10n.planned;
      statusIcon = MdiIcons.calendar;
    } else {
      statusColor = Colors.green;
      statusText = l10n.valid;
      statusIcon = MdiIcons.checkCircle;
    }

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MaintenanceDiagnosticScreen(
              scrollToDocuments: true,
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: ThemeService.professionalNeutrals['gray_50']!,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: ThemeService.professionalNeutrals['gray_200']!,
          ),
        ),
        child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  translationService.translateDocumentName(document.name, l10n),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ThemeService.professionalNeutrals['gray_900']!,
                    fontSize: 11,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  document.expirationDate != null
                      ? '${l10n.daysRemaining}: ${daysRemaining > 0 ? daysRemaining : 0} ${l10n.days}'
                      : l10n.dateNotDefined,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeService.professionalNeutrals['gray_600']!,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              statusText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 8,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    ),
    );
  }

  // Méthode pour compter seulement les entretiens de base actifs (exclure documents et "autre")
  int _getActiveMaintenanceItemsCount(MaintenanceProvider provider) {
    return provider.maintenanceItems
        .where((item) =>
            item.isActive &&
            item.category.toLowerCase() != 'autre' &&
            !item.name.startsWith('[CUSTOM]')) // Exclure les entretiens personnalisés temporaires
        .length;
  }

  // Méthodes de navigation
  void _navigateToKilometerInput(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const KilometerInputScreen()),
    );
  }

  void _navigateToVehicleConfig(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const VehicleConfigScreen()),
    );
  }

  void _navigateToSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  void _navigateToNotifications(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NotificationsScreen()),
    );
  }

  void _navigateToTotalMaintenance(BuildContext context) {
    // Naviguer vers le diagnostic qui montre tous les entretiens avec leurs statuts
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MaintenanceDiagnosticScreen(),
      ),
    );
  }

  void _navigateToStatistics(BuildContext context) {
    // Navigation directe sans publicité interstitielle
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const StatisticsScreen()),
    );
  }

  // Navigation vers les entretiens filtrés par statut
  void _navigateToUrgentMaintenance(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    // Naviguer vers le diagnostic avec filtre "Urgent"
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const MaintenanceDiagnosticScreen(initialFilter: 'Urgent'),
      ),
    );
  }

  void _navigateToApproachingMaintenance(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    // Naviguer vers le diagnostic avec filtre "À planifier"
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const MaintenanceDiagnosticScreen(initialFilter: 'À planifier'),
      ),
    );
  }

  void _navigateToOkMaintenance(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    // Naviguer vers le diagnostic avec filtre "OK"
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            const MaintenanceDiagnosticScreen(initialFilter: 'OK'),
      ),
    );
  }

  // Widget tableau de bord moderne
  Widget _buildVehicleInfoCard(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    final l10n = AppLocalizations.of(context);
    final themeService = context.read<ThemeService>();

    if (provider.currentVehicle == null) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              themeService.primaryColor.withValues(alpha: 0.1),
              themeService.primaryColor.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: themeService.primaryColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: themeService.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: themeService.primaryColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    MdiIcons.viewDashboard,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    l10n.dashboard,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: ThemeService.professionalNeutrals['gray_900']!,
                      fontSize: 18,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(MdiIcons.alertCircle, color: Colors.orange, size: 18),
                  const SizedBox(width: 12),
                  Text(
                    l10n.noVehicleConfigured,
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    final vehicle = provider.currentVehicle!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            themeService.primaryColor.withValues(alpha: 0.1),
            themeService.primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: themeService.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: themeService.primaryColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: themeService.primaryColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: themeService.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  MdiIcons.viewDashboard,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  l10n.dashboard,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: ThemeService.professionalNeutrals['gray_900']!,
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          Row(
            children: [
              Expanded(
                child: _buildSimpleInfoRow(
                  l10n.vehicle,
                  vehicle.vehicleName,
                  MdiIcons.car,
                  themeService.primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSimpleInfoRow(
                  l10n.mileage,
                  '${vehicle.currentKilometers} km',
                  MdiIcons.speedometer,
                  Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildSimpleInfoRow(
                  l10n.brand,
                  vehicle.brand,
                  MdiIcons.carInfo,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSimpleInfoRow(
                  l10n.lastUpdate,
                  _formatDate(vehicle.lastKmUpdate),
                  MdiIcons.clockOutline,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleInfoRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: ThemeService.professionalNeutrals['gray_600']!,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Padding(
          padding: const EdgeInsets.only(left: 24),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: ThemeService.professionalNeutrals['gray_900']!,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Afficher un message de succès moderne
  void _showModernSnackBar(
    BuildContext context,
    String message,
    Color color,
    IconData icon, {
    int durationSeconds = 4,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(icon, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
        duration: Duration(seconds: durationSeconds),
      ),
    );
  }
}
