import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/notification_settings.dart';
import '../services/notification_settings_service.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  NotificationSettings? _settings;
  bool _isLoading = true;
  bool _isSaving = false;

  final _dailyDistanceController = TextEditingController();
  final _reminderKmController = TextEditingController();
  final _documentReminderDaysController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _dailyDistanceController.dispose();
    _reminderKmController.dispose();
    _documentReminderDaysController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await NotificationSettingsService.loadSettings();
      setState(() {
        _settings = settings;
        _dailyDistanceController.text = settings.dailyDistanceKm.toString();
        _reminderKmController.text = settings.reminderKmBefore.toString();
        _documentReminderDaysController.text = settings.documentReminderDaysBefore.toString();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur de chargement: $e')),
        );
      }
    }
  }

  Future<void> _saveSettings() async {
    if (_settings == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final updatedSettings = _settings!.copyWith(
        dailyDistanceKm: double.tryParse(_dailyDistanceController.text) ?? _settings!.dailyDistanceKm,
        reminderKmBefore: int.tryParse(_reminderKmController.text) ?? _settings!.reminderKmBefore,
        documentReminderDaysBefore: int.tryParse(_documentReminderDaysController.text) ?? _settings!.documentReminderDaysBefore,
      );

      await NotificationSettingsService.saveSettings(updatedSettings);
      setState(() {
        _settings = updatedSettings;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Paramètres sauvegardés avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur de sauvegarde: $e')),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  void _updateBoolSetting(String key, bool value) {
    if (_settings == null) return;

    setState(() {
      switch (key) {
        case 'notificationsEnabled':
          _settings = _settings!.copyWith(notificationsEnabled: value);
          break;

        case 'maintenanceNotificationsEnabled':
          _settings = _settings!.copyWith(maintenanceNotificationsEnabled: value);
          break;
        case 'documentNotificationsEnabled':
          _settings = _settings!.copyWith(documentNotificationsEnabled: value);
          break;
      }
    });
    _saveSettings();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Configuration Notifications',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1E40AF),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              onPressed: _saveSettings,
              icon: const Icon(Icons.save),
              tooltip: 'Sauvegarder',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _settings == null
              ? const Center(child: Text('Erreur de chargement'))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeaderSection(),
                      const SizedBox(height: 20),
                      _buildGeneralNotificationsSection(),
                      const SizedBox(height: 20),
                      _buildMaintenanceNotificationsSection(),
                      const SizedBox(height: 20),
                      _buildDocumentNotificationsSection(),
                      const SizedBox(height: 20),
                      _buildAdvancedSection(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildHeaderSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1E40AF).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.notifications_active,
                    color: Color(0xFF1E40AF),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Paramètres de Notifications',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Configurez vos préférences de notifications pour ne jamais manquer un entretien important.',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 13,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralNotificationsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.cog,
                  color: Colors.blue,
                  size: 18,
                ),
                const SizedBox(width: 10),
                const Text(
                  'Paramètres Généraux',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Activer les notifications',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                Transform.scale(
                  scale: 0.8,
                  child: Switch(
                    value: _settings!.notificationsEnabled,
                    onChanged: (value) => _updateBoolSetting('notificationsEnabled', value),
                    activeColor: const Color(0xFF1E40AF),
                  ),
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceNotificationsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.wrench,
                  color: Colors.orange,
                  size: 18,
                ),
                const SizedBox(width: 10),
                const Text(
                  'Notifications d\'Entretien',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Notifications d\'entretien',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                Transform.scale(
                  scale: 0.8,
                  child: Switch(
                    value: _settings!.maintenanceNotificationsEnabled,
                    onChanged: (value) => _updateBoolSetting('maintenanceNotificationsEnabled', value),
                    activeColor: const Color(0xFF1E40AF),
                  ),
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }

  Widget _buildDocumentNotificationsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.fileDocument,
                  color: Colors.green,
                  size: 18,
                ),
                const SizedBox(width: 10),
                const Text(
                  'Notifications de Documents',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Notifications de documents',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                Transform.scale(
                  scale: 0.8,
                  child: Switch(
                    value: _settings!.documentNotificationsEnabled,
                    onChanged: (value) => _updateBoolSetting('documentNotificationsEnabled', value),
                    activeColor: const Color(0xFF1E40AF),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.tune,
                  color: Colors.purple,
                  size: 18,
                ),
                const SizedBox(width: 10),
                const Text(
                  'Paramètres Avancés',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _dailyDistanceController,
              keyboardType: TextInputType.number,
              style: const TextStyle(fontSize: 13),
              enabled: _settings!.notificationsEnabled && _settings!.maintenanceNotificationsEnabled,
              decoration: InputDecoration(
                labelText: 'Distance quotidienne (km)',
                labelStyle: const TextStyle(fontSize: 12),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                prefixIcon: Icon(MdiIcons.roadVariant, size: 18),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _reminderKmController,
              keyboardType: TextInputType.number,
              style: const TextStyle(fontSize: 13),
              enabled: _settings!.notificationsEnabled && _settings!.maintenanceNotificationsEnabled,
              decoration: InputDecoration(
                labelText: 'Rappel entretien (km avant)',
                labelStyle: const TextStyle(fontSize: 12),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                prefixIcon: Icon(MdiIcons.speedometer, size: 18),
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _documentReminderDaysController,
              keyboardType: TextInputType.number,
              style: const TextStyle(fontSize: 13),
              enabled: _settings!.notificationsEnabled && _settings!.documentNotificationsEnabled,
              decoration: InputDecoration(
                labelText: 'Rappel documents (jours avant)',
                labelStyle: const TextStyle(fontSize: 12),
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                prefixIcon: Icon(MdiIcons.fileDocument, size: 18),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
