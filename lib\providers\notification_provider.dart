import 'package:flutter/foundation.dart';
import '../models/notification_settings.dart';
import '../models/vehicle_config.dart';
import '../models/maintenance_item.dart';
import '../models/document_item.dart';
import '../services/notification_settings_service.dart';
import '../services/predictive_service.dart';
import '../services/system_notification_service.dart';

class NotificationProvider extends ChangeNotifier {
  NotificationSettings _settings = const NotificationSettings();
  List<MaintenancePrediction> _predictions = [];
  List<NotificationAlert> _alerts = [];
  bool _isLoading = false;

  // Getters
  NotificationSettings get settings => _settings;
  List<MaintenancePrediction> get predictions => _predictions;
  List<NotificationAlert> get alerts => _alerts;
  bool get isLoading => _isLoading;

  // Getters pour les paramètres spécifiques
  bool get areNotificationsEnabled => _settings.notificationsEnabled;
  bool get areMaintenanceNotificationsEnabled => 
      _settings.notificationsEnabled && _settings.maintenanceNotificationsEnabled;
  bool get areDocumentNotificationsEnabled => 
      _settings.notificationsEnabled && _settings.documentNotificationsEnabled;
  bool get arePredictiveNotificationsEnabled => 
      _settings.notificationsEnabled && 
      _settings.maintenanceNotificationsEnabled && 
      _settings.predictiveNotificationsEnabled;
  double get dailyDistance => _settings.dailyDistanceKm;

  /// Initialise le provider
  Future<void> initialize() async {
    _setLoading(true);
    try {
      // Initialiser le service de notifications système
      await SystemNotificationService.initialize();
      await loadSettings();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du NotificationProvider: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Charge les paramètres de notification
  Future<void> loadSettings() async {
    try {
      _settings = await NotificationSettingsService.loadSettings();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement des paramètres: $e');
    }
  }

  /// Met à jour un paramètre spécifique
  Future<bool> updateSetting<T>(String key, T value) async {
    try {
      final success = await NotificationSettingsService.updateSetting(key, value);
      if (success) {
        await loadSettings(); // Recharger les paramètres
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour du paramètre $key: $e');
      return false;
    }
  }

  /// Sauvegarde les paramètres complets
  Future<bool> saveSettings(NotificationSettings newSettings) async {
    try {
      final success = await NotificationSettingsService.saveSettings(newSettings);
      if (success) {
        _settings = newSettings;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des paramètres: $e');
      return false;
    }
  }

  /// Génère les prédictions de maintenance
  void generatePredictions({
    required VehicleConfig vehicle,
    required List<MaintenanceItem> maintenanceItems,
  }) {
    if (!arePredictiveNotificationsEnabled) {
      _predictions = [];
      notifyListeners();
      return;
    }

    try {
      _predictions = PredictiveService.generateMaintenancePredictions(
        items: maintenanceItems,
        vehicle: vehicle,
        dailyDistanceKm: _settings.dailyDistanceKm,
        daysAhead: 90,
      );
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de la génération des prédictions: $e');
      _predictions = [];
      notifyListeners();
    }
  }

  /// Génère les alertes de notification
  Future<void> generateAlerts({
    required VehicleConfig vehicle,
    required List<MaintenanceItem> maintenanceItems,
    required List<DocumentItem> documentItems,
  }) async {
    _alerts = [];

    try {
      // Alertes de maintenance
      if (areMaintenanceNotificationsEnabled) {
        _generateMaintenanceAlerts(vehicle, maintenanceItems);
      }

      // Alertes de documents
      if (areDocumentNotificationsEnabled) {
        _generateDocumentAlerts(documentItems);
      }

      // Alertes prédictives (intégrées comme notifications officielles)
      if (arePredictiveNotificationsEnabled) {
        _generatePredictiveAlerts(vehicle);
      }

      // Trier les alertes par priorité
      _alerts.sort((a, b) => b.priority.compareTo(a.priority));

      // Planifier les notifications système
      await _scheduleSystemNotifications(vehicle, maintenanceItems, documentItems);

      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de la génération des alertes: $e');
    }
  }

  /// Planifie les notifications système
  Future<void> _scheduleSystemNotifications(
    VehicleConfig vehicle,
    List<MaintenanceItem> maintenanceItems,
    List<DocumentItem> documentItems,
  ) async {
    try {
      await SystemNotificationService.scheduleNotificationsForVehicle(
        vehicle: vehicle,
        maintenanceItems: maintenanceItems,
        documentItems: documentItems,
      );
    } catch (e) {
      debugPrint('Erreur lors de la planification des notifications système: $e');
    }
  }

  /// Génère les alertes de maintenance
  void _generateMaintenanceAlerts(VehicleConfig vehicle, List<MaintenanceItem> items) {
    for (final item in items) {
      debugPrint('🔍 Traitement entretien: ${item.name} - Catégorie: ${item.category} - Actif: ${item.isActive}');

      if (!item.isActive) {
        debugPrint('❌ Entretien inactif ignoré: ${item.name}');
        continue;
      }

      // Exclure les entretiens de type "autre" et les entretiens personnalisés
      if (item.category.toLowerCase() == 'autre') {
        debugPrint('❌ Entretien "autre" exclu: ${item.name}');
        continue;
      }

      if (item.name.startsWith('[CUSTOM]')) {
        debugPrint('❌ Entretien personnalisé exclu: ${item.name}');
        continue;
      }

      debugPrint('✅ Entretien traité: ${item.name}');

      if (item.isMaintenanceDue) {
        _alerts.add(NotificationAlert(
          id: 'maintenance_due_${item.id}',
          title: 'Entretien en retard',
          message: '${item.name} est en retard de ${item.kmUntilNextMaintenance.abs()} km',
          type: NotificationAlertType.urgent,
          priority: 3,
          relatedItemId: item.id?.toString(),
          createdAt: DateTime.now(),
        ));
      } else if (item.isMaintenanceApproaching) {
        _alerts.add(NotificationAlert(
          id: 'maintenance_approaching_${item.id}',
          title: 'Entretien à prévoir',
          message: '${item.name} dans ${item.kmUntilNextMaintenance} km',
          type: NotificationAlertType.reminder,
          priority: 2,
          relatedItemId: item.id?.toString(),
          createdAt: DateTime.now(),
        ));
      }
    }
  }

  /// Génère les alertes de documents
  void _generateDocumentAlerts(List<DocumentItem> documents) {
    final now = DateTime.now();
    final reminderDays = _settings.documentReminderDaysBefore;

    for (final document in documents) {
      if (document.expirationDate == null) continue;

      final daysUntilExpiration = document.expirationDate!.difference(now).inDays;

      if (daysUntilExpiration < 0) {
        _alerts.add(NotificationAlert(
          id: 'document_expired_${document.id}',
          title: 'Document expiré',
          message: '${document.name} a expiré il y a ${daysUntilExpiration.abs()} jour(s)',
          type: NotificationAlertType.urgent,
          priority: 3,
          relatedItemId: document.id?.toString(),
          createdAt: DateTime.now(),
        ));
      } else if (daysUntilExpiration <= reminderDays) {
        _alerts.add(NotificationAlert(
          id: 'document_expiring_${document.id}',
          title: 'Document à renouveler',
          message: '${document.name} expire dans $daysUntilExpiration jour(s)',
          type: NotificationAlertType.warning,
          priority: 2,
          relatedItemId: document.id?.toString(),
          createdAt: DateTime.now(),
        ));
      }
    }
  }

  /// Génère les alertes prédictives
  void _generatePredictiveAlerts(VehicleConfig vehicle) {
    // Vérifier si une mise à jour du kilométrage est recommandée (après 15 jours)
    final shouldUpdate = PredictiveService.shouldUpdateKilometers(
      vehicle: vehicle,
      dailyDistanceKm: _settings.dailyDistanceKm,
      thresholdDays: 15, // Rappel après 15 jours comme demandé
    );

    if (shouldUpdate) {
      final daysSinceLastUpdate = DateTime.now().difference(vehicle.lastKmUpdate).inDays;
      final recommendedKm = PredictiveService.getRecommendedKilometers(
        vehicle: vehicle,
        dailyDistanceKm: _settings.dailyDistanceKm,
      );

      _alerts.add(NotificationAlert(
        id: 'kilometer_reminder',
        title: 'Rappel de mise à jour',
        message: 'Il y a $daysSinceLastUpdate jours depuis votre dernière mise à jour. Kilométrage suggéré: ${recommendedKm.round()} km',
        type: NotificationAlertType.reminder,
        priority: 2,
        relatedItemId: 'kilometer_update',
        createdAt: DateTime.now(),
      ));
    }

    // Alertes basées sur les prédictions (seulement si pas déjà d'alerte pour cet entretien)
    for (final prediction in _predictions) {
      if (prediction.daysUntil <= _settings.reminderDaysBefore) {
        // Vérifier si une alerte existe déjà pour cet entretien
        final hasExistingAlert = _alerts.any((alert) =>
          alert.relatedItemId == prediction.item.id?.toString());

        if (!hasExistingAlert) {
          _alerts.add(NotificationAlert(
            id: 'maintenance_predicted_${prediction.item.id}',
            title: 'Entretien à prévoir',
            message: '${prediction.item.name} dans ${prediction.daysUntil} jour(s) (${_formatDate(prediction.predictedDate)})',
            type: NotificationAlertType.reminder,
            priority: 1, // Priorité plus faible que les alertes normales
            relatedItemId: prediction.item.id?.toString(),
            createdAt: DateTime.now(),
          ));
        }
      }
    }
  }

  /// Marque une alerte comme lue
  void markAlertAsRead(String alertId) {
    _alerts.removeWhere((alert) => alert.id == alertId);
    notifyListeners();
  }

  /// Efface toutes les alertes
  void clearAllAlerts() {
    _alerts.clear();
    notifyListeners();
  }

  /// Obtient le nombre d'alertes par type
  int getAlertCount(NotificationAlertType type) {
    return _alerts.where((alert) => alert.type == type).length;
  }

  /// Obtient le nombre total d'alertes
  int get totalAlertCount => _alerts.length;

  /// Obtient les alertes urgentes
  List<NotificationAlert> get urgentAlerts => 
      _alerts.where((alert) => alert.type == NotificationAlertType.urgent).toList();

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Classes pour les alertes de notification
class NotificationAlert {
  final String id;
  final String title;
  final String message;
  final NotificationAlertType type;
  final int priority;
  final String? relatedItemId;
  final DateTime createdAt;

  const NotificationAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    this.relatedItemId,
    required this.createdAt,
  });
}

enum NotificationAlertType {
  urgent,
  warning,
  reminder,
  predictive,
  info,
}
