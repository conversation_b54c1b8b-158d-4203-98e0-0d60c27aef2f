import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../widgets/presentation_animations.dart';
import '../l10n/app_localizations.dart';

class PresentationScreen extends StatefulWidget {
  const PresentationScreen({super.key});

  @override
  State<PresentationScreen> createState() => _PresentationScreenState();
}

class _PresentationScreenState extends State<PresentationScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  List<PresentationSlide> _slides = [];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initializeSlides();
  }

  void _initializeSlides() {
    final l10n = AppLocalizations.of(context);
    _slides = [
      PresentationSlide(
        title: l10n.presentationWelcomeTitle,
        description: l10n.presentationWelcomeDesc,
        imagePath: 'assets/images/LogoMenuLat.png',
        backgroundColor: const Color(0xFF2196F3),
      ),
      PresentationSlide(
        title: l10n.presentationMaintenanceTitle,
        description: l10n.presentationMaintenanceDesc,
        icon: MdiIcons.carWrench,
        backgroundColor: const Color(0xFF4CAF50),
      ),
      PresentationSlide(
        title: l10n.presentationNotificationsTitle,
        description: l10n.presentationNotificationsDesc,
        icon: MdiIcons.bellRing,
        backgroundColor: const Color(0xFFFF9800),
      ),
      PresentationSlide(
        title: l10n.presentationHistoryTitle,
        description: l10n.presentationHistoryDesc,
        icon: MdiIcons.history,
        backgroundColor: const Color(0xFF9C27B0),
      ),
      PresentationSlide(
        title: l10n.presentationStatsTitle,
        description: l10n.presentationStatsDesc,
        icon: MdiIcons.chartLine,
        backgroundColor: const Color(0xFF009688),
      ),
      PresentationSlide(
        title: l10n.presentationDiagnosticTitle,
        description: l10n.presentationDiagnosticDesc,
        icon: MdiIcons.carCog,
        backgroundColor: const Color(0xFF795548),
      ),
      PresentationSlide(
        title: l10n.presentationReadyTitle,
        description: l10n.presentationReadyDesc,
        icon: MdiIcons.checkCircle,
        backgroundColor: const Color(0xFF3F51B5),
      ),
    ];
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextSlide() {
    if (_currentPage < _slides.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Dernière slide - fermer la présentation
      Navigator.pop(context);
    }
  }

  void _previousSlide() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipPresentation() {
    Navigator.pop(context);
  }

  void _goToSlide(int index) {
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_slides.isEmpty) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Barre de progression
            _buildProgressBar(),

            // Contenu des slides
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _slides.length,
                itemBuilder: (context, index) {
                  return _buildSlide(_slides[index]);
                },
              ),
            ),

            // Boutons de navigation
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    final l10n = AppLocalizations.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Bouton Passer
          TextButton(
            onPressed: _skipPresentation,
            child: Text(
              l10n.skip,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ),
          
          const Spacer(),
          
          // Indicateurs de progression (cliquables)
          Row(
            children: List.generate(
              _slides.length,
              (index) => GestureDetector(
                onTap: () => _goToSlide(index),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  width: index == _currentPage ? 12 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: index == _currentPage
                        ? _slides[_currentPage].backgroundColor
                        : Colors.grey[300],
                  ),
                ),
              ),
            ),
          ),
          
          const Spacer(),
          
          // Numéro de page
          Text(
            '${_currentPage + 1}/${_slides.length}',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSlide(PresentationSlide slide) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            slide.backgroundColor.withValues(alpha: 0.1),
            slide.backgroundColor.withValues(alpha: 0.05),
          ],
        ),
      ),
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image ou icône avec animation
          if (slide.imagePath != null)
            ScaleInAnimation(
              delay: const Duration(milliseconds: 200),
              child: Image.asset(
                slide.imagePath!,
                height: 200,
                width: 200,
                fit: BoxFit.contain,
              ),
            )
          else if (slide.icon != null)
            ScaleInAnimation(
              delay: const Duration(milliseconds: 200),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: slide.backgroundColor.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  slide.icon!,
                  size: 80,
                  color: slide.backgroundColor,
                ),
              ),
            ),
          
          const SizedBox(height: 40),

          // Titre avec animation
          SlideInAnimation(
            delay: const Duration(milliseconds: 400),
            beginOffset: const Offset(0, 0.2),
            child: Text(
              slide.title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: slide.backgroundColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 20),

          // Description avec animation
          SlideInAnimation(
            delay: const Duration(milliseconds: 600),
            beginOffset: const Offset(0, 0.2),
            child: Text(
              slide.description,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    final l10n = AppLocalizations.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Bouton Précédent
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _previousSlide,
                icon: const Icon(Icons.arrow_back),
                label: Text(l10n.previousStep),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            )
          else
            const Expanded(child: SizedBox()),

          const SizedBox(width: 16),

          // Bouton Suivant/Terminer
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _nextSlide,
              icon: Icon(
                _currentPage == _slides.length - 1
                    ? Icons.check
                    : Icons.arrow_forward,
              ),
              label: Text(
                _currentPage == _slides.length - 1
                    ? l10n.finish
                    : l10n.next,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _slides[_currentPage].backgroundColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Modèle pour les slides de présentation
class PresentationSlide {
  final String title;
  final String description;
  final String? imagePath;
  final IconData? icon;
  final Color backgroundColor;

  PresentationSlide({
    required this.title,
    required this.description,
    this.imagePath,
    this.icon,
    required this.backgroundColor,
  });
}
