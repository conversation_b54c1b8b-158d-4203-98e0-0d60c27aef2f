import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/notification_provider.dart';
import '../services/navigation_service.dart';

class NotificationBadge extends StatelessWidget {
  final VoidCallback? onTap;
  final double size;
  final Color? iconColor;

  const NotificationBadge({
    super.key,
    this.onTap,
    this.size = 24,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        final alertCount = notificationProvider.totalAlertCount;
        final hasUrgentAlerts = notificationProvider.urgentAlerts.isNotEmpty;

        return Stack(
          children: [
            IconButton(
              onPressed: onTap,
              icon: Icon(
                hasUrgentAlerts ? MdiIcons.bellAlert : MdiIcons.bellOutline,
                size: size,
                color: iconColor ?? (hasUrgentAlerts ? Colors.red : null),
              ),
              tooltip: alertCount > 0 
                  ? '$alertCount notification${alertCount > 1 ? 's' : ''}'
                  : 'Notifications',
            ),
            if (alertCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: hasUrgentAlerts ? Colors.red : Colors.orange,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.white, width: 1),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    alertCount > 99 ? '99+' : alertCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class NotificationPanel extends StatelessWidget {
  const NotificationPanel({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        final alerts = notificationProvider.alerts;

        if (alerts.isEmpty) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      MdiIcons.bellOff,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Aucune notification',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Toutes vos notifications apparaîtront ici',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return Column(
          children: alerts.map((alert) => NotificationCard(alert: alert)).toList(),
        );
      },
    );
  }
}

class NotificationCard extends StatelessWidget {
  final NotificationAlert alert;

  const NotificationCard({
    super.key,
    required this.alert,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: _buildIcon(),
        title: Text(
          alert.title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              alert.message,
              style: const TextStyle(fontSize: 13),
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(alert.createdAt),
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.close, size: 18),
          onPressed: () {
            context.read<NotificationProvider>().markAlertAsRead(alert.id);
          },
          tooltip: 'Marquer comme lu',
        ),
        onTap: () => _handleNotificationTap(context),
        isThreeLine: true,
      ),
    );
  }

  /// Gère le tap sur une notification pour naviguer vers la page appropriée
  void _handleNotificationTap(BuildContext context) {
    // Marquer la notification comme lue
    context.read<NotificationProvider>().markAlertAsRead(alert.id);

    // Naviguer selon le type de notification
    switch (alert.id) {
      case 'kilometer_reminder':
        NavigationService.navigateToKilometerUpdate(context);
        break;

      case String id when id.startsWith('maintenance_due_'):
        final itemId = id.replaceFirst('maintenance_due_', '');
        NavigationService.navigateToDiagnosticWithFilter(context, 'Urgent', focusItemId: itemId);
        break;

      case String id when id.startsWith('maintenance_approaching_'):
        final itemId = id.replaceFirst('maintenance_approaching_', '');
        NavigationService.navigateToDiagnosticWithFilter(context, 'À planifier', focusItemId: itemId);
        break;

      case String id when id.startsWith('document_expired_') || id.startsWith('document_expiring_'):
        NavigationService.navigateToDiagnosticDocuments(context);
        break;

      case String id when id.startsWith('maintenance_predicted_'):
        final itemId = id.replaceFirst('maintenance_predicted_', '');
        NavigationService.navigateToDiagnosticWithFilter(context, 'À planifier', focusItemId: itemId);
        break;

      default:
        // Pour les autres types, naviguer vers la page d'accueil
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
        break;
    }
  }

  Widget _buildIcon() {
    IconData iconData;
    Color iconColor;

    switch (alert.type) {
      case NotificationAlertType.urgent:
        iconData = MdiIcons.alertCircle;
        iconColor = Colors.red;
        break;
      case NotificationAlertType.warning:
        iconData = MdiIcons.alert;
        iconColor = Colors.orange;
        break;
      case NotificationAlertType.reminder:
        iconData = MdiIcons.clockAlert;
        iconColor = Colors.blue;
        break;
      case NotificationAlertType.predictive:
        iconData = MdiIcons.autoFix;
        iconColor = Colors.purple;
        break;
      case NotificationAlertType.info:
        iconData = MdiIcons.informationOutline;
        iconColor = Colors.green;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inHours < 1) {
      return 'Il y a ${difference.inMinutes} min';
    } else if (difference.inDays < 1) {
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}

class PredictiveInsightCard extends StatelessWidget {
  const PredictiveInsightCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        final predictions = notificationProvider.predictions;

        if (predictions.isEmpty || !notificationProvider.arePredictiveNotificationsEnabled) {
          return const SizedBox.shrink();
        }

        final nextPrediction = predictions.first;

        return Card(
          color: Colors.purple.withValues(alpha: 0.05),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      MdiIcons.autoFix,
                      color: Colors.purple,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Prévision Intelligente',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  'Prochain entretien prévu',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  nextPrediction.item.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      MdiIcons.calendar,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${_formatDate(nextPrediction.predictedDate)} (dans ${nextPrediction.daysUntil} jours)',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      MdiIcons.speedometer,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '~${nextPrediction.predictedKilometers.round()} km',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: nextPrediction.confidenceColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: nextPrediction.confidenceColor),
                      ),
                      child: Text(
                        nextPrediction.confidenceText,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: nextPrediction.confidenceColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
