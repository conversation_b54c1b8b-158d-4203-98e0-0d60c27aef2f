import 'app_localizations.dart';

class AppLocalizationsFr extends AppLocalizations {
  // Navigation et interface générale
  @override
  String get appTitle => 'Carosti';
  @override
  String get home => 'Accueil';
  @override
  String get settings => 'Paramètres';
  @override
  String get notifications => 'Notifications';
  @override
  String get statistics => 'Statistiques';
  @override
  String get about => 'À propos';
  @override
  String get help => 'Aide';
  @override
  String get language => 'Langue';
  @override
  String get theme => 'Thème';
  @override
  String get currency => 'Devise';
  @override
  String get premium => 'Premium';
  @override
  String get backup => 'Sauvegarde';
  @override
  String get restore => 'Restaurer';
  @override
  String get export => 'Exporter';
  @override
  String get import => 'Importer';
  @override
  String get cancel => 'Annuler';
  @override
  String get save => 'Enregistrer';
  @override
  String get delete => 'Supprimer';
  @override
  String get edit => 'Modifier';
  @override
  String get add => 'Ajouter';
  @override
  String get confirm => 'Confirmer';
  @override
  String get yes => 'Oui';
  @override
  String get no => 'Non';
  @override
  String get ok => 'OK';
  @override
  String get close => 'Fermer';
  @override
  String get back => 'Retour';
  @override
  String get next => 'Suivant';
  @override
  String get previous => 'Précédent';
  @override
  String get loading => 'Chargement...';
  @override
  String get error => 'Erreur';
  @override
  String get success => 'Succès';
  @override
  String get warning => 'Attention';
  @override
  String get info => 'Information';

  // Véhicule
  @override
  String get vehicle => 'Véhicule';
  @override
  String get vehicles => 'Véhicules';
  @override
  String get addVehicle => 'Ajouter un véhicule';
  @override
  String get editVehicle => 'Modifier le véhicule';
  @override
  String get deleteVehicle => 'Supprimer le véhicule';
  @override
  String get vehicleName => 'Nom du véhicule';
  @override
  String get brand => 'Marque';
  @override
  String get model => 'Modèle';
  @override
  String get year => 'Année';
  @override
  String get currentKilometers => 'Kilométrage actuel';
  @override
  String get licensePlate => 'Plaque d\'immatriculation';
  @override
  String get engineType => 'Type de moteur';
  @override
  String get selectVehicle => 'Sélectionner un véhicule';
  @override
  String get noVehicleSelected => 'Aucun véhicule sélectionné';
  @override
  String get defaultVehicle => 'Véhicule par défaut';
  @override
  String get switchVehicle => 'Changer de véhicule';
  @override
  String get vehicleInfo => 'Informations du véhicule';
  @override
  String get updateKilometers => 'Mettre à jour le kilométrage';
  @override
  String get enterKilometers => 'Saisir le kilométrage';
  @override
  String get kilometersUpdated => 'Kilométrage mis à jour';
  @override
  String get invalidKilometers => 'Kilométrage invalide';

  // Entretien
  @override
  String get maintenance => 'Entretien';
  @override
  String get maintenanceItems => 'Éléments d\'entretien';
  @override
  String get addMaintenance => 'Ajouter un entretien';
  @override
  String get editMaintenance => 'Modifier l\'entretien';
  @override
  String get deleteMaintenance => 'Supprimer l\'entretien';
  @override
  String get maintenanceName => 'Nom de l\'entretien';
  @override
  String get maintenanceDescription => 'Description de l\'entretien';
  @override
  String get category => 'Catégorie';
  @override
  String get interval => 'Intervalle';
  @override
  String get defaultInterval => 'Intervalle par défaut';
  @override
  String get customInterval => 'Intervalle personnalisé';
  @override
  String get lastMaintenance => 'Dernier entretien';
  @override
  String get nextMaintenance => 'Prochain entretien';
  @override
  String get maintenanceDue => 'Entretien dû';
  @override
  String get maintenanceApproaching => 'Entretien proche';
  @override
  String get maintenanceUpToDate => 'Entretien à jour';
  @override
  String get performMaintenance => 'Effectuer l\'entretien';
  @override
  String get maintenanceCompleted => 'Entretien terminé';
  @override
  String get maintenanceHistory => 'Historique d\'entretien';
  @override
  String get maintenanceDate => 'Date d\'entretien';
  @override
  String get maintenanceCost => 'Coût de l\'entretien';
  @override
  String get maintenanceLocation => 'Lieu d\'entretien';
  @override
  String get mechanicName => 'Nom du mécanicien';
  @override
  String get notes => 'Notes';
  @override
  String get kmUntilNext => 'Km jusqu\'au prochain';
  @override
  String get usagePercentage => 'Pourcentage d\'usure';
  @override
  String get active => 'Actif';
  @override
  String get inactive => 'Inactif';

  // Catégories d'entretien
  @override
  String get engine => 'Moteur';
  @override
  String get brakes => 'Freins';
  @override
  String get tires => 'Pneus';
  @override
  String get fluids => 'Fluides';
  @override
  String get filters => 'Filtres';
  @override
  String get electrical => 'Électrique';
  @override
  String get bodywork => 'Carrosserie';
  @override
  String get interior => 'Intérieur';
  @override
  String get safety => 'Sécurité';
  @override
  String get other => 'Autre';

  // Documents
  @override
  String get documents => 'Documents';
  @override
  String get addDocument => 'Ajouter un document';
  @override
  String get editDocument => 'Modifier le document';
  @override
  String get deleteDocument => 'Supprimer le document';
  @override
  String get documentName => 'Nom du document';
  @override
  String get documentDescription => 'Description du document';
  @override
  String get expirationDate => 'Date d\'expiration';
  @override
  String get companyName => 'Nom de l\'entreprise';
  @override
  String get documentNumber => 'Numéro du document';
  @override
  String get documentExpired => 'Document expiré';
  @override
  String get documentExpiring => 'Document expirant';
  @override
  String get documentValid => 'Document valide';
  @override
  String get insurance => 'Assurance';
  @override
  String get registration => 'Carte grise';
  @override
  String get technicalInspection => 'Contrôle technique';
  @override
  String get warranty => 'Garantie';
  @override
  String get invoice => 'Facture';
  @override
  String get receipt => 'Reçu';
  @override
  String get manual => 'Manuel';
  @override
  String get certificate => 'Certificat';

  // Statistiques
  @override
  String get totalMaintenanceItems => 'Total des éléments d\'entretien';
  @override
  String get itemsDue => 'Éléments dus';
  @override
  String get itemsApproaching => 'Éléments proches';
  @override
  String get itemsUpToDate => 'Éléments à jour';
  @override
  String get overview => 'Vue d\'ensemble';
  @override
  String get quickActions => 'Actions rapides';
  @override
  String get urgent => 'Urgents';
  @override
  String get upcoming => 'À venir';
  @override
  String get upToDate => 'À jour';
  @override
  String get toProcess => 'à traiter';
  @override
  String get toPlan => 'à planifier';
  @override
  String get inOrder => 'en ordre';
  @override
  String get configureVehicle => 'Config véhicule';
  @override
  String get configure => 'Configurer';
  @override
  String get updateKm => 'Maj KM';
  @override
  String get update => 'Mettre à jour';
  @override
  String get diagnostics => 'Diagnostic';
  @override
  String get analyze => 'Analyser';
  @override
  String get privacyPolicy => 'Politique de confidentialité';
  @override
  String get importExport => 'Import/Export';
  @override
  String get updateApp => 'Mise à jour';
  @override
  String get rateApp => 'Évaluation';
  @override
  String get quit => 'Quitter';
  @override
  String get hello => 'Bonjour!';
  @override
  String get version => 'Version';
  @override
  String get newVehicle => 'Nouveau véhicule';
  @override
  String get vehicleConfiguration => 'Configuration véhicule';
  @override
  String get information => 'Informations';
  @override
  String get nameRequired => 'Nom requis';
  @override
  String get brandRequired => 'Marque requise';
  @override
  String get modelRequired => 'Modèle requis';
  @override
  String get yearRequired => 'Année requise';
  @override
  String get invalidYear => 'Année invalide';
  @override
  String get technical => 'Technique';
  @override
  String get kilometersRequired => 'Kilométrage requis';
  @override
  String get saving => 'Sauvegarde...';
  @override
  String get deleteVehicleTitle => '⚠️ Supprimer véhicule';
  @override
  String get deleteVehicleMessage => 'Cette action supprimera DÉFINITIVEMENT :\n\n'
      '• Le véhicule et sa configuration\n'
      '• Tout l\'historique des entretiens\n'
      '• Toutes les données de l\'application\n\n'
      'Cette action est IRRÉVERSIBLE !';
  @override
  String get history => 'Historique';
  @override
  String get all => 'Tous';
  @override
  String get search => 'Rechercher';
  @override
  String get filter => 'Filtrer';
  @override
  String get searchInHistory => 'Rechercher dans l\'historique...';
  @override
  String get performed => 'Effectué';
  @override
  String get editKilometers => 'Modifier le kilométrage';
  @override
  String get deleteThisMaintenance => 'Supprimer cet entretien';
  @override
  String get cost => 'Coût';
  @override
  String get location => 'Lieu';
  @override
  String get mechanic => 'Mécanicien';
  @override
  String get noHistoryFound => 'Aucun historique trouvé';
  @override
  String get maintenanceWillAppearHere => 'Les entretiens effectués apparaîtront ici';
  @override
  String get deletedMaintenance => 'Entretien supprimé';
  @override
  String get deleteMaintenanceTitle => 'Supprimer l\'entretien';
  @override
  String get deleteMaintenanceConfirmation => 'Êtes-vous sûr de vouloir supprimer cet entretien ?';
  @override
  String get thisActionCannotBeUndone => 'Cette action ne peut pas être annulée.';
  @override
  String get soon => 'BIENTÔT';
  @override
  String get progression => 'Progression';
  @override
  String get currentKm => 'Km actuel';
  @override
  String get last => 'Dernier';
  @override
  String get remaining => 'Reste';
  @override
  String get exceededBy => 'Dépassé de';
  @override
  String get actions => 'Actions';
  @override
  String get perform => 'Effectuer';
  @override
  String get noHistory => 'Aucun historique';
  @override
  String get totalCost => 'Coût total';
  @override
  String get averageCost => 'Coût moyen';
  @override
  String get lastUpdate => 'Dernière mise à jour';
  @override
  String get maintenanceOverview => 'Aperçu de l\'entretien';
  @override
  String get costAnalysis => 'Analyse des coûts';
  @override
  String get monthlyStats => 'Statistiques mensuelles';
  @override
  String get yearlyStats => 'Statistiques annuelles';
  @override
  String get mostExpensive => 'Le plus cher';
  @override
  String get mostFrequent => 'Le plus fréquent';
  @override
  String get upcomingMaintenance => 'Entretiens à venir';

  // Notifications
  @override
  String get notificationTitle => 'Titre de notification';
  @override
  String get notificationBody => 'Corps de notification';
  @override
  String get enableNotifications => 'Activer les notifications';
  @override
  String get notificationSettings => 'Paramètres de notification';
  @override
  String get dailyReminder => 'Rappel quotidien';
  @override
  String get weeklyReminder => 'Rappel hebdomadaire';
  @override
  String get monthlyReminder => 'Rappel mensuel';
  @override
  String get maintenanceReminder => 'Rappel d\'entretien';
  @override
  String get documentReminder => 'Rappel de document';
  @override
  String get reminderTime => 'Heure du rappel';
  @override
  String get noNotifications => 'Aucune notification';
  @override
  String get markAsRead => 'Marquer comme lu';
  @override
  String get clearAll => 'Tout effacer';

  // Erreurs et messages
  @override
  String get errorLoadingData => 'Erreur lors du chargement des données';
  @override
  String get errorSavingData => 'Erreur lors de l\'enregistrement des données';
  @override
  String get errorDeletingData => 'Erreur lors de la suppression des données';
  @override
  String get errorInvalidInput => 'Saisie invalide';
  @override
  String get errorNetworkConnection => 'Erreur de connexion réseau';
  @override
  String get errorDatabaseConnection => 'Erreur de connexion à la base de données';
  @override
  String get errorPermissionDenied => 'Permission refusée';
  @override
  String get errorFileNotFound => 'Fichier non trouvé';
  @override
  String get errorUnknown => 'Erreur inconnue';
  @override
  String get dataLoadedSuccessfully => 'Données chargées avec succès';
  @override
  String get dataSavedSuccessfully => 'Données enregistrées avec succès';
  @override
  String get dataDeletedSuccessfully => 'Données supprimées avec succès';
  @override
  String get operationCompleted => 'Opération terminée';
  @override
  String get operationFailed => 'Opération échouée';
  @override
  String get confirmDelete => 'Confirmer la suppression';
  @override
  String get confirmDeleteMessage => 'Êtes-vous sûr de vouloir supprimer cet élément ?';
  @override
  String get cannotUndoAction => 'Cette action ne peut pas être annulée';

  // Premium
  @override
  String get premiumFeatures => 'Fonctionnalités Premium';
  @override
  String get upgradeToPremium => 'Passer à Premium';
  @override
  String get premiumBenefits => 'Avantages Premium';
  @override
  String get unlimitedVehicles => 'Véhicules illimités';
  @override
  String get advancedStatistics => 'Statistiques avancées';
  @override
  String get cloudBackup => 'Sauvegarde cloud';
  @override
  String get prioritySupport => 'Support prioritaire';
  @override
  String get adFree => 'Sans publicité';
  @override
  String get customThemes => 'Thèmes personnalisés';
  @override
  String get exportData => 'Exporter les données';
  @override
  String get premiumUser => 'Utilisateur Premium';
  @override
  String get freeUser => 'Utilisateur gratuit';
  @override
  String get purchasePremium => 'Acheter Premium';
  @override
  String get restorePurchases => 'Restaurer les achats';

  // Sauvegarde et restauration
  @override
  String get backupData => 'Sauvegarder les données';
  @override
  String get restoreData => 'Restaurer les données';
  @override
  String get exportToFile => 'Exporter vers un fichier';
  @override
  String get importFromFile => 'Importer depuis un fichier';
  @override
  String get selectFile => 'Sélectionner un fichier';
  @override
  String get backupCreated => 'Sauvegarde créée';
  @override
  String get dataRestored => 'Données restaurées';
  @override
  String get backupFailed => 'Échec de la sauvegarde';
  @override
  String get restoreFailed => 'Échec de la restauration';
  @override
  String get invalidBackupFile => 'Fichier de sauvegarde invalide';
  @override
  String get backupInProgress => 'Sauvegarde en cours';
  @override
  String get restoreInProgress => 'Restauration en cours';

  // Mise à jour
  @override
  String get updateAvailable => 'Mise à jour disponible';
  @override
  String get updateNow => 'Mettre à jour maintenant';
  @override
  String get updateLater => 'Mettre à jour plus tard';
  @override
  String get newVersion => 'Nouvelle version';
  @override
  String get currentVersion => 'Version actuelle';
  @override
  String get whatsNew => 'Nouveautés';
  @override
  String get updateFeatures => 'Fonctionnalités de mise à jour';
  @override
  String get downloadUpdate => 'Télécharger la mise à jour';
  @override
  String get installUpdate => 'Installer la mise à jour';

  // Diagnostic
  @override
  String get diagnostic => 'Diagnostic';
  @override
  String get runDiagnostic => 'Exécuter le diagnostic';
  @override
  String get diagnosticResults => 'Résultats du diagnostic';
  @override
  String get systemHealth => 'Santé du système';
  @override
  String get databaseIntegrity => 'Intégrité de la base de données';
  @override
  String get performanceCheck => 'Vérification des performances';
  @override
  String get storageUsage => 'Utilisation du stockage';
  @override
  String get cacheSize => 'Taille du cache';
  @override
  String get clearCache => 'Vider le cache';
  @override
  String get optimizeDatabase => 'Optimiser la base de données';
  @override
  String get repairDatabase => 'Réparer la base de données';

  // Unités et formats
  @override
  String get kilometers => 'Kilomètres';
  @override
  String get km => 'km';
  @override
  String get days => 'jours';
  @override
  String get months => 'mois';
  @override
  String get years => 'années';
  @override
  String get date => 'Date';
  @override
  String get time => 'Heure';
  @override
  String get percentage => 'Pourcentage';
  @override
  String get total => 'Total';
  @override
  String get average => 'Moyenne';
  @override
  String get minimum => 'Minimum';
  @override
  String get maximum => 'Maximum';

  // Actions communes
  @override
  String get sort => 'Trier';
  @override
  String get sortBy => 'Trier par';
  @override
  String get ascending => 'Croissant';
  @override
  String get descending => 'Décroissant';
  @override
  String get refresh => 'Actualiser';
  @override
  String get reload => 'Recharger';
  @override
  String get reset => 'Réinitialiser';
  @override
  String get clear => 'Effacer';
  @override
  String get apply => 'Appliquer';
  @override
  String get retry => 'Réessayer';
  @override
  String get undo => 'Annuler';
  @override
  String get redo => 'Refaire';
  @override
  String get copy => 'Copier';
  @override
  String get paste => 'Coller';
  @override
  String get share => 'Partager';
  @override
  String get print => 'Imprimer';
  @override
  String get preview => 'Aperçu';
  @override
  String get fullScreen => 'Plein écran';
  @override
  String get minimize => 'Réduire';
  @override
  String get maximize => 'Agrandir';

  // Messages contextuels
  @override
  String get noDataAvailable => 'Aucune donnée disponible';
  @override
  String get noItemsFound => 'Aucun élément trouvé';
  @override
  String get noResultsFound => 'Aucun résultat trouvé';
  @override
  String get emptyList => 'Liste vide';
  @override
  String get addFirstItem => 'Ajouter le premier élément';
  @override
  String get getStarted => 'Commencer';
  @override
  String get welcomeMessage => 'Bienvenue dans Carosti';
  @override
  String get tutorialTitle => 'Tutoriel';
  @override
  String get skipTutorial => 'Ignorer le tutoriel';
  @override
  String get nextStep => 'Étape suivante';
  @override
  String get previousStep => 'Étape précédente';
  @override
  String get finish => 'Terminer';
  @override
  String get congratulations => 'Félicitations';
  @override
  String get allDone => 'Tout est fait';

  // Textes manquants pour la traduction complète
  @override
  String get dashboard => 'Tableau de bord';
  @override
  String get noVehicleConfigured => 'Aucun véhicule configuré';
  @override
  String get current => 'Actuel';
  @override
  String get remainingKilometers => 'Kilomètres restants';
  @override
  String get wear => 'Usure';
  @override
  String get noMaintenanceNeeded => 'Aucun entretien imminent';
  @override
  String get expired => 'EXPIRÉ';
  @override
  String get valid => 'VALIDE';
  @override
  String get planned => 'PLANIFIÉ';
  @override
  String get daysRemaining => 'Reste';
  @override
  String get dateNotDefined => 'Date non définie';
  @override
  String get mileage => 'Kilométrage';
  @override
  String get today => 'Aujourd\'hui';
  @override
  String get yesterday => 'Hier';
  @override
  String get daysAgo => 'Il y a';

  // About Screen
  @override
  String get description => 'Description';
  @override
  String get appDescription => 'CAROSTI est votre assistant intelligent pour l\'entretien automobile :\n\n'
      '• Suivi précis du kilométrage\n'
      '• Scan automatique du tableau de bord\n'
      '• Rappels intelligents d\'entretien\n'
      '• Historique détaillé des maintenances\n'
      '• Intervalles personnalisables\n'
      '• Interface moderne et intuitive';
  @override
  String get features => 'Fonctionnalités';
  @override
  String get mileageTracking => 'Suivi du kilométrage';
  @override
  String get mileageTrackingDesc => 'Saisie manuelle ou scan automatique';
  @override
  String get maintenanceManagement => 'Gestion des entretiens';
  @override
  String get maintenanceManagementDesc => 'Vidange, filtres, freins, pneus, etc.';
  @override
  String get ocrScan => 'Scan OCR';
  @override
  String get ocrScanDesc => 'Lecture automatique du tableau de bord';
  @override
  String get notificationsFeature => 'Notifications';
  @override
  String get notificationsFeatureDesc => 'Rappels intelligents d\'entretien';
  @override
  String get completeHistory => 'Historique complet';
  @override
  String get completeHistoryDesc => 'Suivi de toutes les maintenances';
  @override
  String get support => 'Support';
  @override
  String get supportDescription => 'Pour toute question, suggestion ou problème technique, n\'hésitez pas à nous contacter.';
  @override
  String get contactEmail => 'Email de contact';
  @override
  String get copyEmail => 'Copier Email';
  @override
  String get emailCopied => 'Email copié ! 📧';
  @override
  String get visitWebsite => 'Visiter site';
  @override
  String get cannotOpenWebsite => 'Impossible d\'ouvrir le site web';
  @override
  String get errorOpening => 'Erreur lors de l\'ouverture';

  // Camera Scan Screen
  @override
  String get enterMileage => 'Saisir le kilométrage';
  @override
  String get scanMileage => 'Scanner le kilométrage';
  @override
  String get autoScanUnavailable => 'Scan automatique temporairement indisponible';
  @override
  String get autoScanUnavailableDesc => 'La fonctionnalité de scan automatique du tableau de bord sera disponible dans une prochaine version de l\'application.';
  @override
  String get processing => 'Traitement...';
  @override
  String get manualEntry => 'Saisie manuelle';
  @override
  String get enterValidMileage => 'Veuillez entrer un kilométrage valide';

  // Help FAQ Screen
  @override
  String get helpFaq => 'Aide & FAQ';
  @override
  String get maintenances => 'Entretiens';
  @override
  String get data => 'Données';
  @override
  String get discoverApp => 'Découvrir l\'app';
  @override
  String get discoverAppDesc => 'Regardez notre présentation interactive pour découvrir toutes les fonctionnalités';
  @override
  String get viewPresentation => 'Voir la présentation';
  @override
  String get quickGuide => 'Guide rapide';
  @override
  String get followMaintenances => 'Suivre entretiens';
  @override
  String get consultStats => 'Consulter Stats';
  @override
  String get noQuestionFound => 'Aucune question trouvée';

  // FAQ Questions and Answers
  @override
  String get faqAddVehicleQ => 'Comment ajouter un véhicule ?';
  @override
  String get faqAddVehicleA => 'Allez dans Paramètres > Gestion des véhicules > Ajouter un véhicule. Renseignez les informations de votre véhicule (marque, modèle, année, kilométrage actuel).';
  @override
  String get faqPerformMaintenanceQ => 'Comment effectuer un entretien ?';
  @override
  String get faqPerformMaintenanceA => 'Depuis l\'écran d\'accueil, appuyez sur un élément d\'entretien, puis sur "Effectuer l\'entretien". Vous pouvez également aller dans Diagnostic > sélectionner l\'entretien.';
  @override
  String get faqNotificationsQ => 'Pourquoi je ne reçois pas de notifications ?';
  @override
  String get faqNotificationsA => 'Vérifiez que les notifications sont activées dans les paramètres de votre téléphone. Les notifications apparaissent quand un entretien approche de son échéance.';
  @override
  String get faqCustomIntervalsQ => 'Comment personnaliser les intervalles ?';
  @override
  String get faqCustomIntervalsA => 'Allez dans Intervalles > sélectionnez l\'entretien > modifiez l\'intervalle personnalisé. Vous pouvez adapter les intervalles selon vos habitudes de conduite.';
  @override
  String get faqAddCustomMaintenanceQ => 'Comment ajouter un entretien personnalisé ?';
  @override
  String get faqAddCustomMaintenanceA => 'Dans Statistiques, appuyez sur le bouton "+" puis sélectionnez "Autre". Saisissez le nom de l\'opération, le coût et le kilométrage.';
  @override
  String get faqBackupDataQ => 'Comment sauvegarder mes données ?';
  @override
  String get faqBackupDataA => 'Allez dans Paramètres > Sauvegarde et restauration > Exporter les données. Vous pouvez importer et exporter vos données à n\'importe quel moment.';
  @override
  String get faqChangeCurrencyQ => 'Comment changer la devise ?';
  @override
  String get faqChangeCurrencyA => 'Allez dans Paramètres > Devise pour sélectionner votre devise locale. Tous les coûts seront affichés dans cette devise.';
  @override
  String get faqWearPercentageQ => 'Que signifie le pourcentage d\'usure ?';
  @override
  String get faqWearPercentageA => 'Le pourcentage d\'usure indique l\'état de vos pièces basé sur le kilométrage. 100% = entretien nécessaire, 80% = bientôt nécessaire.';

  // Maintenance Detail Screen
  @override
  String get informations => 'Informations';
  @override
  String get reloadHistoryComment => 'Recharger l\'historique si l\'entretien a été effectué';

  // Engine categories
  @override
  String get braking => 'Freinage';
  @override
  String get cooling => 'Refroidissement';
  @override
  String get comfort => 'Confort';

  // Perform Maintenance Screen
  @override
  String get maintenanceInfoComment => 'Informations sur l\'élément d\'entretien';
  @override
  String get details => 'Détails';
  @override
  String get maintenanceKmLabel => 'Kilométrage de l\'entretien';
  @override
  String get maintenanceKmHint => 'km lors de l\'entretien';
  @override
  String get enterKmError => 'Veuillez saisir le kilométrage';
  @override
  String get invalidKmError => 'Kilométrage invalide';
  @override
  String get garage => 'Garage';
  @override
  String get observations => 'Observations';
  @override
  String get maintenanceHelpText => 'Date d\'entretien';
  @override
  String get maintenanceSaved => 'Entretien enregistré';

  // Maintenance Intervals Screen
  @override
  String get intervals => 'Intervalles';
  @override
  String get extinguisher => 'Extincteur';
  @override
  String get extinguisherControl => 'Contrôle de l\'extincteur';
  @override
  String get security => 'Sécurité';
  @override
  String get configIntervals => 'Config intervalles';
  @override
  String get restoreDefault => 'Restaurer défaut';
  @override
  String get custom => 'Custom';
  @override
  String get defaultValue => 'Défaut';
  @override
  String get modify => 'Modifier';
  @override
  String get modifyInterval => 'Modifier intervalle';
  @override
  String get enterValidInterval => 'Veuillez entrer un intervalle valide';
  @override
  String get intervalUpdated => 'Intervalle mis à jour';
  @override
  String get intervalRestored => 'Intervalle restauré';
  @override
  String get restoreDefaultValues => 'Restaurer les valeurs par défaut';
  @override
  String get featureInDevelopment => 'Fonctionnalité en développement';
  @override
  String get on => 'ON';
  @override
  String get off => 'OFF';
  @override
  String get activate => 'Activer';
  @override
  String get deactivate => 'Désactiver';

  // Maintenance Items Names
  @override
  String get oilChangeEngine => 'Vidange Huile Moteur';
  @override
  String get oilChangeEngineDesc => 'Changement huile moteur et filtre';
  @override
  String get fuelFilter => 'Filtre à Gasoil';
  @override
  String get fuelFilterDesc => 'Remplacement filtre carburant';
  @override
  String get airFilter => 'Filtre à Air';
  @override
  String get airFilterDesc => 'Remplacement filtre air moteur';
  @override
  String get cabinFilter => 'Filtre Habitacle';
  @override
  String get cabinFilterDesc => 'Remplacement filtre climatisation';
  @override
  String get timingBelt => 'Courroie de Distribution';
  @override
  String get timingBeltDesc => 'Vérification et remplacement courroie de distribution';
  @override
  String get coolantFluid => 'Liquide de Refroidissement';
  @override
  String get coolantFluidDesc => 'Vidange système refroidissement';
  @override
  String get brakeFluid => 'Liquide de Frein';
  @override
  String get brakeFluidDesc => 'Vidange liquide frein';
  @override
  String get brakePads => 'Plaquettes de Frein';
  @override
  String get brakePadsDesc => 'Vérification et remplacement plaquettes';
  @override
  String get tiresItem => 'Pneus';
  @override
  String get tiresItemDesc => 'Vérification usure et rotation';

  // Document Items Names
  @override
  String get insuranceDoc => 'Assurance';
  @override
  String get insuranceDocDesc => 'Assurance automobile obligatoire';
  @override
  String get technicalControlDoc => 'Contrôle technique';
  @override
  String get technicalControlDocDesc => 'Contrôle technique périodique';
  @override
  String get extinguisherDoc => 'Extincteur';
  @override
  String get extinguisherDocDesc => 'Contrôle de l\'extincteur';

  // Categories Names
  @override
  String get engineCategory => 'Moteur';
  @override
  String get filtersCategory => 'Filtres';
  @override
  String get brakingCategory => 'Freinage';
  @override
  String get tiresCategory => 'Pneumatiques';
  @override
  String get coolingCategory => 'Refroidissement';
  @override
  String get comfortCategory => 'Confort';
  @override
  String get insuranceCategory => 'Assurance';
  @override
  String get controlCategory => 'Contrôle';
  @override
  String get securityCategory => 'Sécurité';

  // Home Screen
  @override
  String get appName => 'CAROSTI';
  @override
  String get toSchedule => 'À planifier';

  // Maintenance Diagnostic Screen
  @override
  String get recreateDatabase => 'Recréer base';
  @override
  String get noVehicle => 'Aucun véhicule';
  @override
  String get noMaintenanceRecorded => 'Aucun entretien enregistré';
  @override
  String get modifyKm => 'Modifier km';
  @override
  String get kmUpdated => 'Km mis à jour';
  @override
  String get comparison => 'Comparaison';
  @override
  String get comparisonKm => 'Comparaison Km';
  @override
  String get difference => 'Différence';
  @override
  String get expiration => 'Expiration';
  @override
  String get remains => 'Reste';
  @override
  String get company => 'Compagnie';
  @override
  String get selectDate => 'Sélectionner une date';

  // Settings Screen
  @override
  String get configVehicle => 'Config véhicule';
  @override
  String get myVehicles => 'Mes véhicules';
  @override
  String get vehiclesConfigured => 'véhicule(s) configuré(s)';
  @override
  String get defaultAndCustomIntervals => 'Intervalles défaut et personnalisés';
  @override
  String get notificationReminders => 'Rappels d\'entretien et prévisions';
  @override
  String get application => 'Application';
  @override
  String get colorsAndAppearance => 'Couleurs et apparence';
  @override
  String get testLocalization => 'Test Localisation';
  @override
  String get testTranslations => 'Tester les traductions';
  @override
  String get appInfo => 'Infos application';
  @override
  String get exportSuccess => 'Export réussi!';
  @override
  String get exportError => 'Erreur lors de l\'export';
  @override
  String get closeLoadingIndicator => 'Fermer l\'indicateur de chargement en cas d\'erreur';
  @override
  String get redirectToHome => 'Rediriger vers la page d\'accueil si on n\'y est pas déjà';
  @override
  String get availableLanguages => 'Langues disponibles';
  @override
  String get backupAndRestore => 'Sauvegarde et restauration';
  @override
  String get guideAndFaq => 'Guide et FAQ';

  // Vehicle Config Screen
  @override
  String get brandHint => 'Peugeot';
  @override
  String get engineHint => 'Essence';
  @override
  String get kmHint => '50000';
  @override
  String get kmSuffix => 'km';
  @override
  String get vehicleModified => 'Véhicule modifié';
  @override
  String get vehicleAdded => 'Véhicule ajouté';
  @override
  String get noCurrentVehicle => 'Aucun véhicule actuel trouvé';
  @override
  String get myVehicle => 'Mon Véhicule';
  @override
  String get vehicleDeletedDefaultCreated => 'Véhicule supprimé et véhicule par défaut créé';

  // Statistics Screen
  @override
  String get analysisPeriod => 'Période d\'analyse';
  @override
  String get oneYear => '1 année';
  @override
  String get everything => 'Tout';
  @override
  String get distribution => 'Répartition';
  @override
  String get maintenanceCosts => 'Coûts d\'Entretien';
  @override
  String get thisMonth => 'Ce mois';
  @override
  String get thisYear => 'Cette année';
  @override
  String get noMaintenanceCostRecorded => 'Aucun coût d\'entretien enregistré';
  @override
  String get distributionByMaintenance => 'Répartition par Entretien';
  @override
  String get predictions => 'Prédictions';
  @override
  String get nextMaintenances => 'Prochains Entretiens';
  @override
  String get now => 'Maintenant';
  @override
  String get tomorrow => 'Demain';
  @override
  String get inDays => 'Dans {} jour(s)';
  @override
  String get inWeeks => 'Dans {} semaine(s)';
  @override
  String get weeksAgo => 'Il y a {} semaine(s)';
  @override
  String get monthsAgo => 'Il y a {} mois';
  @override
  String get yearsAgo => 'Il y a {} an(s)';
  @override
  String get maintenanceType => 'Type d\'entretien';
  @override
  String get enterOperation => 'Saisir l\'opération';
  @override
  String get addToHistory => 'Ajouter à l\'historique';
  @override
  String get modifyMaintenance => 'Modifier Entretien';
  @override
  String get confirmDeletion => 'Confirmer suppression';

  // Perform Maintenance Screen
  @override
  String get maintenanceDetails => 'Détails';
  @override
  String get enterMileageValidation => 'Veuillez saisir le kilométrage';
  @override
  String get invalidMileage => 'Kilométrage invalide';
  @override
  String get maintenanceRecorded => 'Entretien enregistré';

  // Maintenance Detail Screen
  @override
  String get exceeded => 'Dépassé de';

  // Additional Maintenance Diagnostic
  @override
  String get recreateConfirm => 'Confirmer la recréation de la base de données ?';
  @override
  String get databaseRecreated => 'Base de données recréée';

  // Additional Statistics Screen
  @override
  String get operation => 'Opération';

  // Help FAQ Screen - Additional
  @override
  String get configureVehicleStep => 'Configurer véhicule';
  @override
  String get updateKmStep => 'Actualiser Km';
  @override
  String get followMaintenanceStep => 'Suivre entretiens';
  @override
  String get consultStatsStep => 'Consulter Stats';
  @override
  String get addNewVehicleQuestion => 'Comment ajouter un nouveau véhicule ?';
  @override
  String get addNewVehicleAnswer => 'Allez dans Paramètres > Gestion des véhicules > Ajouter un véhicule. Renseignez les informations de votre véhicule (marque, modèle, année, kilométrage actuel).';
  @override
  String get scheduleMaintenanceQuestion => 'Comment programmer un entretien ?';
  @override
  String get scheduleMaintenanceAnswer => 'Depuis l\'écran d\'accueil, appuyez sur un élément d\'entretien, puis sur "Effectuer l\'entretien". Vous pouvez également aller dans Diagnostic > sélectionner l\'entretien.';
  @override
  String get noNotificationsQuestion => 'Pourquoi je ne reçois pas de notifications ?';
  @override
  String get noNotificationsAnswer => 'Vérifiez que les notifications sont activées dans les paramètres de votre téléphone. Les notifications apparaissent quand un entretien approche de son échéance.';
  @override
  String get modifyIntervalsQuestion => 'Comment modifier les intervalles d\'entretien ?';
  @override
  String get modifyIntervalsAnswer => 'Allez dans Intervalles > sélectionnez l\'entretien > modifiez l\'intervalle personnalisé. Vous pouvez adapter les intervalles selon vos habitudes de conduite.';
  @override
  String get addCustomOperationQuestion => 'Comment ajouter une opération personnalisée ?';
  @override
  String get addCustomOperationAnswer => 'Dans Statistiques, appuyez sur le bouton "+" puis sélectionnez "Autre". Saisissez le nom de l\'opération, le coût et le kilométrage.';
  @override
  String get exportDataQuestion => 'Comment exporter mes données ?';
  @override
  String get exportDataAnswer => 'Allez dans Paramètres > Sauvegarde et restauration > Exporter les données. Vous pouvez importer et exporter vos données à n\'importe quel moment.';
  @override
  String get changeCurrencyQuestion => 'Comment changer la devise ?';
  @override
  String get changeCurrencyAnswer => 'Allez dans Paramètres > Devise pour sélectionner votre devise locale. Tous les coûts seront affichés dans cette devise.';
  @override
  String get wearPercentageQuestion => 'Que signifient les pourcentages d\'usure ?';
  @override
  String get wearPercentageAnswer => 'Le pourcentage d\'usure indique l\'état de vos pièces basé sur le kilométrage. 100% = entretien nécessaire, 80% = bientôt nécessaire.';

  // Privacy Policy Screen
  @override
  String get privacyPolicyTitle => 'Politique de Confidentialité';
  @override
  String get dataCollection => 'Collecte des Données';
  @override
  String get dataCollectionText => 'CAROSTI collecte uniquement les données nécessaires au fonctionnement de l\'application : informations sur votre véhicule, historique d\'entretien et préférences utilisateur. Toutes les données sont stockées localement sur votre appareil.';
  @override
  String get dataUsage => 'Utilisation des Données';
  @override
  String get dataUsageText => 'Vos données sont utilisées exclusivement pour vous fournir les services de l\'application : suivi d\'entretien, rappels, statistiques. Aucune donnée n\'est transmise à des tiers sans votre consentement explicite.';
  @override
  String get dataSharing => 'Partage des Données';
  @override
  String get dataSharingText => 'CAROSTI ne partage, ne vend, ni ne loue vos données personnelles à des tiers. Vos informations restent privées et sécurisées sur votre appareil.';
  @override
  String get dataSecurity => 'Sécurité des Données';
  @override
  String get dataSecurityText => 'Nous mettons en œuvre des mesures de sécurité appropriées pour protéger vos données contre l\'accès non autorisé, la modification ou la suppression. Le stockage local garantit un contrôle total de vos données.';
  @override
  String get userRights => 'Vos Droits';
  @override
  String get userRightsText => 'Vous avez le droit d\'accéder, modifier ou supprimer vos données à tout moment via les paramètres de l\'application. Vous pouvez également exporter vos données ou réinitialiser l\'application.';
  @override
  String get contactUs => 'Nous Contacter';
  @override
  String get contactUsText => 'Pour toute question concernant cette politique de confidentialité, contactez-nous à : <EMAIL> | Site web : carosti-app.blogspot.com';
  @override
  String get lastUpdated => 'Dernière mise à jour : Janvier 2025';

  // Import Export Messages
  @override
  String get fileSelected => 'Fichier sélectionné';
  @override
  String get exportInProgress => 'Export en cours...';
  @override
  String get exportComplete => 'Export terminé';
  @override
  String get restoreComplete => 'Restauration terminée';
  @override
  String get invalidFileFormat => 'Format de fichier invalide';
  @override
  String get fileNotFound => 'Fichier non trouvé';
  @override
  String get permissionDenied => 'Permission refusée';
  @override
  String get storageAccessRequired => 'Accès au stockage requis';
  @override
  String get chooseBackupFile => 'Choisir un fichier de sauvegarde';
  @override
  String get createBackup => 'Créer une sauvegarde';
  @override
  String get restoreFromBackup => 'Restaurer depuis une sauvegarde';
  @override
  String get dataWillBeReplaced => 'Toutes les données actuelles seront remplacées';
  @override
  String get confirmRestore => 'Confirmer la restauration';
  @override
  String get backupSavedTo => 'Sauvegarde enregistrée dans';

  // App Update Messages
  @override
  String get newVersionAvailable => 'Nouvelle version disponible';
  @override
  String get currentVersionText => 'Version actuelle';
  @override
  String get latestVersionText => 'Dernière version';
  @override
  String get improvements => 'Améliorations';
  @override
  String get downloadingUpdate => 'Téléchargement de la mise à jour...';
  @override
  String get updateDownloaded => 'Mise à jour téléchargée';
  @override
  String get updateFailed => 'Échec de la mise à jour';
  @override
  String get appUpToDate => 'Application à jour';
  @override
  String get updateRequired => 'Mise à jour requise';
  @override
  String get criticalUpdate => 'Mise à jour critique';
  @override
  String get skipThisVersion => 'Ignorer cette version';

  // App Rating Messages
  @override
  String get rateAppTitle => 'Évaluer CAROSTI';
  @override
  String get rateAppMessage => 'Aimez-vous utiliser CAROSTI ? Votre avis nous aide à améliorer l\'application !';
  @override
  String get rateAppPositive => 'J\'adore ! ⭐⭐⭐⭐⭐';
  @override
  String get rateAppNegative => 'Peut mieux faire';
  @override
  String get rateAppNeutral => 'C\'est correct';
  @override
  String get enjoyingApp => 'Appréciez-vous CAROSTI ?';
  @override
  String get wouldYouRate => 'Pourriez-vous nous évaluer ?';
  @override
  String get feedbackTitle => 'Vos commentaires';
  @override
  String get feedbackMessage => 'Dites-nous comment nous pouvons améliorer CAROSTI pour vous !';
  @override
  String get sendFeedback => 'Envoyer commentaires';
  @override
  String get thankYouRating => 'Merci pour votre évaluation !';
  @override
  String get rateOnStore => 'Évaluer sur le Store';
  @override
  String get maybeLater => 'Peut-être plus tard';
  @override
  String get noThanks => 'Non merci';
  @override
  String get howCanImprove => 'Comment pouvons-nous nous améliorer ?';
  @override
  String get tellUsMore => 'Dites-nous en plus...';
  @override
  String get submitFeedback => 'Envoyer le feedback';
  @override
  String get feedbackSent => 'Feedback envoyé ! Merci !';
  @override
  String get openStore => 'Ouvrir le Store';

  // Additional Help FAQ
  @override
  String get parameters => 'Paramètres';

  // Import Export Additional
  @override
  String get importData => 'Importer';
}