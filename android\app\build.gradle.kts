import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

// Chargement des propriétés de signature
val keystoreProperties = Properties()
val keystorePropertiesFile = rootProject.file("key.properties")
println("🔍 Recherche keystore dans: ${keystorePropertiesFile.absolutePath}")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
    println("✅ Fichier key.properties chargé")
} else {
    println("❌ Fichier key.properties non trouvé")
}

android {
    namespace = "com.carosti.app"
    compileSdk = 35  // Android 15 - Requis par Google Play Store
    // ndkVersion = flutter.ndkVersion  // Commented out to avoid NDK issues

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        // Activer le desugaring pour flutter_local_notifications
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    signingConfigs {
        create("release") {
            if (keystorePropertiesFile.exists() &&
                keystoreProperties["keyAlias"] != null &&
                keystoreProperties["keyPassword"] != null &&
                keystoreProperties["storeFile"] != null &&
                keystoreProperties["storePassword"] != null) {

                keyAlias = keystoreProperties["keyAlias"] as String
                keyPassword = keystoreProperties["keyPassword"] as String
                storeFile = file(keystoreProperties["storeFile"] as String)
                storePassword = keystoreProperties["storePassword"] as String

                println("✅ Signature configurée avec keystore: ${keystoreProperties["storeFile"]}")
            } else {
                println("⚠️ Keystore non trouvé ou propriétés manquantes")
                println("   - Fichier existe: ${keystorePropertiesFile.exists()}")
                if (keystorePropertiesFile.exists()) {
                    println("   - keyAlias: ${keystoreProperties["keyAlias"]}")
                    println("   - storeFile: ${keystoreProperties["storeFile"]}")
                }
            }
        }
    }

    defaultConfig {
        applicationId = "com.carosti.app"
        minSdk = 21  // Android 5.0 minimum pour AdMob
        targetSdk = 35  // Android 15 - Requis par Google Play Store (Nov 2025)
        versionCode = 3  // Incrémenté pour la nouvelle version
        versionName = "1.2.0"  // Nouvelle version avec Android 15
    }

    buildTypes {
        debug {
            if (keystorePropertiesFile.exists() &&
                keystoreProperties["keyAlias"] != null) {
                signingConfig = signingConfigs.getByName("release")
                println("🔧 DEBUG: Utilisation signature personnalisée")
            } else {
                println("🔧 DEBUG: Utilisation signature par défaut")
            }
        }
        release {
            if (keystorePropertiesFile.exists() &&
                keystoreProperties["keyAlias"] != null) {
                signingConfig = signingConfigs.getByName("release")
                println("🚀 RELEASE: Utilisation signature personnalisée")
            } else {
                println("⚠️ RELEASE: Signature personnalisée non trouvée!")
            }
            isMinifyEnabled = false
            isShrinkResources = false
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Desugaring pour flutter_local_notifications
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}
