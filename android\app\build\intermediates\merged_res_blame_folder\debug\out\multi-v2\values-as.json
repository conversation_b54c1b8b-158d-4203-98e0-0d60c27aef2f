{"logs": [{"outputFile": "com.carosti.app-mergeDebugResources-50:/values-as/values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dad6ecdc4610e29e4f98bd0a0d7ae39e\\transformed\\browser-1.8.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5790,5982,6088,6196", "endColumns": "107,105,107,105", "endOffsets": "5893,6083,6191,6297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c700a73c2e433e10f13879e3255c75cd\\transformed\\appcompat-1.2.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,884,975,1068,1163,1257,1357,1450,1545,1639,1730,1821,1907,2020,2128,2227,2336,2452,2572,2739,2841", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,98,108,115,119,166,101,82", "endOffsets": "208,307,414,505,607,727,804,879,970,1063,1158,1252,1352,1445,1540,1634,1725,1816,1902,2015,2123,2222,2331,2447,2567,2734,2836,2919"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,884,975,1068,1163,1257,1357,1450,1545,1639,1730,1821,1907,2020,2128,2227,2336,2452,2572,2739,8120", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,98,108,115,119,166,101,82", "endOffsets": "208,307,414,505,607,727,804,879,970,1063,1158,1252,1352,1445,1540,1634,1725,1816,1902,2015,2123,2222,2331,2447,2567,2734,2836,8198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\80b747fd0bf560516ae3aa3beeee4d5c\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4562", "endColumns": "125", "endOffsets": "4683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3afbe6dd2c3a497e6596084102e039dc\\transformed\\preference-1.2.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,182,266,349,492,661,752", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "177,261,344,487,656,747,827"}, "to": {"startLines": "54,56,63,75,85,86,87", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5713,5898,6459,7550,8304,8473,8564", "endColumns": "76,83,82,142,168,90,79", "endOffsets": "5785,5977,6537,7688,8468,8559,8639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f200f4a28092d3753dbbadd5663700bb\\transformed\\jetified-play-services-base-18.0.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3591,3699,3852,3975,4085,4215,4337,4450,4688,4831,4940,5090,5215,5348,5501,5561,5627", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "3694,3847,3970,4080,4210,4332,4445,4557,4826,4935,5085,5210,5343,5496,5556,5622,5708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1e115093ecfdcf00d62fda419f1b05b4\\transformed\\jetified-play-services-ads-23.6.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,243,291,344,413,483,579,642,761,872,997,1052,1111,1223,1308,1347,1430,1469,1513,1572,1663,1707", "endColumns": "43,47,52,68,69,95,62,118,110,124,54,58,111,84,38,82,38,43,58,90,43,55", "endOffsets": "242,290,343,412,482,578,641,760,871,996,1051,1110,1222,1307,1346,1429,1468,1512,1571,1662,1706,1762"}, "to": {"startLines": "60,61,62,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6302,6350,6402,6542,6615,6689,6789,6856,6979,7094,7223,7282,7345,7461,7693,7736,7823,7866,7914,7977,8072,8644", "endColumns": "47,51,56,72,73,99,66,122,114,128,58,62,115,88,42,86,42,47,62,94,47,59", "endOffsets": "6345,6397,6454,6610,6684,6784,6851,6974,7089,7218,7277,7340,7456,7545,7731,7818,7861,7909,7972,8067,8115,8699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1ac570f9a9ce8a2badd3127fcdbc117\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2841,2942,3045,3153,3258,3362,3462,8203", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "2937,3040,3148,3253,3357,3457,3586,8299"}}]}]}