class NotificationSettings {
  final bool notificationsEnabled;
  final bool maintenanceNotificationsEnabled;
  final bool documentNotificationsEnabled;
  final double dailyDistanceKm;
  final int reminderDaysBefore;
  final int reminderKmBefore; // Nouveau champ pour les km avant entretien
  final int documentReminderDaysBefore;
  final bool predictiveNotificationsEnabled;
  final int predictiveReminderKm;

  const NotificationSettings({
    this.notificationsEnabled = true,
    this.maintenanceNotificationsEnabled = true,
    this.documentNotificationsEnabled = true,
    this.dailyDistanceKm = 50.0, // 50km par jour par défaut
    this.reminderDaysBefore = 7, // 7 jours avant échéance (déprécié)
    this.reminderKmBefore = 1000, // 1000km avant échéance entretien
    this.documentReminderDaysBefore = 30, // 30 jours avant expiration document
    this.predictiveNotificationsEnabled = true,
    this.predictiveReminderKm = 500, // Rappel 500km avant échéance
  });

  NotificationSettings copyWith({
    bool? notificationsEnabled,
    bool? maintenanceNotificationsEnabled,
    bool? documentNotificationsEnabled,
    double? dailyDistanceKm,
    int? reminderDaysBefore,
    int? reminderKmBefore,
    int? documentReminderDaysBefore,
    bool? predictiveNotificationsEnabled,
    int? predictiveReminderKm,
  }) {
    return NotificationSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      maintenanceNotificationsEnabled: maintenanceNotificationsEnabled ?? this.maintenanceNotificationsEnabled,
      documentNotificationsEnabled: documentNotificationsEnabled ?? this.documentNotificationsEnabled,
      dailyDistanceKm: dailyDistanceKm ?? this.dailyDistanceKm,
      reminderDaysBefore: reminderDaysBefore ?? this.reminderDaysBefore,
      reminderKmBefore: reminderKmBefore ?? this.reminderKmBefore,
      documentReminderDaysBefore: documentReminderDaysBefore ?? this.documentReminderDaysBefore,
      predictiveNotificationsEnabled: predictiveNotificationsEnabled ?? this.predictiveNotificationsEnabled,
      predictiveReminderKm: predictiveReminderKm ?? this.predictiveReminderKm,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'maintenanceNotificationsEnabled': maintenanceNotificationsEnabled,
      'documentNotificationsEnabled': documentNotificationsEnabled,
      'dailyDistanceKm': dailyDistanceKm,
      'reminderDaysBefore': reminderDaysBefore,
      'reminderKmBefore': reminderKmBefore,
      'documentReminderDaysBefore': documentReminderDaysBefore,
      'predictiveNotificationsEnabled': predictiveNotificationsEnabled,
      'predictiveReminderKm': predictiveReminderKm,
    };
  }

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      maintenanceNotificationsEnabled: json['maintenanceNotificationsEnabled'] ?? true,
      documentNotificationsEnabled: json['documentNotificationsEnabled'] ?? true,
      dailyDistanceKm: (json['dailyDistanceKm'] ?? 50.0).toDouble(),
      reminderDaysBefore: json['reminderDaysBefore'] ?? 7,
      reminderKmBefore: json['reminderKmBefore'] ?? 1000,
      documentReminderDaysBefore: json['documentReminderDaysBefore'] ?? 30,
      predictiveNotificationsEnabled: json['predictiveNotificationsEnabled'] ?? true,
      predictiveReminderKm: json['predictiveReminderKm'] ?? 500,
    );
  }

  @override
  String toString() {
    return 'NotificationSettings('
        'notificationsEnabled: $notificationsEnabled, '
        'maintenanceNotificationsEnabled: $maintenanceNotificationsEnabled, '
        'documentNotificationsEnabled: $documentNotificationsEnabled, '
        'dailyDistanceKm: $dailyDistanceKm, '
        'reminderDaysBefore: $reminderDaysBefore, '
        'reminderKmBefore: $reminderKmBefore, '
        'documentReminderDaysBefore: $documentReminderDaysBefore, '
        'predictiveNotificationsEnabled: $predictiveNotificationsEnabled, '
        'predictiveReminderKm: $predictiveReminderKm'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationSettings &&
        other.notificationsEnabled == notificationsEnabled &&
        other.maintenanceNotificationsEnabled == maintenanceNotificationsEnabled &&
        other.documentNotificationsEnabled == documentNotificationsEnabled &&
        other.dailyDistanceKm == dailyDistanceKm &&
        other.reminderDaysBefore == reminderDaysBefore &&
        other.reminderKmBefore == reminderKmBefore &&
        other.documentReminderDaysBefore == documentReminderDaysBefore &&
        other.predictiveNotificationsEnabled == predictiveNotificationsEnabled &&
        other.predictiveReminderKm == predictiveReminderKm;
  }

  @override
  int get hashCode {
    return Object.hash(
      notificationsEnabled,
      maintenanceNotificationsEnabled,
      documentNotificationsEnabled,
      dailyDistanceKm,
      reminderDaysBefore,
      reminderKmBefore,
      documentReminderDaysBefore,
      predictiveNotificationsEnabled,
      predictiveReminderKm,
    );
  }
}
