import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/maintenance_item.dart';
import '../models/vehicle_config.dart';
import '../models/maintenance_history.dart';
import '../models/document_item.dart';
import '../models/default_maintenance_items.dart';
import '../services/database_service.dart';
import '../services/initialization_service.dart';
import '../services/notification_service.dart';
import '../services/data_refresh_service.dart';

class MaintenanceProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final InitializationService _initializationService = InitializationService();
  final NotificationService _notificationService = NotificationService();

  List<MaintenanceItem> _maintenanceItems = [];
  VehicleConfig? _currentVehicle;
  List<VehicleConfig> _allVehicles = [];
  List<MaintenanceHistory> _maintenanceHistory = [];
  List<DocumentItem> _documentItems = [];
  bool _isLoading = false;
  String? _error;

  // Timer pour les mises à jour automatiques
  Timer? _updateTimer;

  // Subscription pour écouter les changements de données
  StreamSubscription<bool>? _refreshSubscription;

  // Getters
  List<MaintenanceItem> get maintenanceItems => _maintenanceItems;
  VehicleConfig? get currentVehicle => _currentVehicle;
  List<VehicleConfig> get allVehicles => _allVehicles;
  List<MaintenanceHistory> get maintenanceHistory => _maintenanceHistory;
  List<DocumentItem> get documentItems => _documentItems;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Getters pour les statistiques basés sur le pourcentage d'usure
  List<MaintenanceItem> get itemsDue => _maintenanceItems
      .where((item) => calculateWearPercentage(item) >= 100)
      .toList();

  List<MaintenanceItem> get itemsApproaching => _maintenanceItems.where((item) {
    final wearPercentage = calculateWearPercentage(item);
    return wearPercentage >= 60 && wearPercentage < 100;
  }).toList();

  // Getter pour les notifications
  NotificationService get notificationService => _notificationService;

  Map<String, List<MaintenanceItem>> get itemsByCategory {
    final Map<String, List<MaintenanceItem>> grouped = {};
    for (final item in _maintenanceItems) {
      if (!grouped.containsKey(item.category)) {
        grouped[item.category] = [];
      }
      grouped[item.category]!.add(item);
    }
    return grouped;
  }

  // Initialisation
  Future<void> initialize() async {
    _setLoading(true);
    try {
      // Vérifier si c'est la première installation
      await _handleFirstInstallation();

      await _initializationService.initializeApp();

      // CHARGER TOUS LES VÉHICULES EN PREMIER
      await _loadAllVehicles();
      debugPrint('🚗 Initialize: ${_allVehicles.length} véhicules chargés');

      await _loadCurrentVehicle();
      debugPrint('🎯 Initialize: Véhicule actuel: ${_currentVehicle?.vehicleName}');

      await _loadMaintenanceItems();
      await _loadDocumentItems();

      // Écouter les notifications de rechargement après import
      _refreshSubscription?.cancel();
      _refreshSubscription = DataRefreshService().refreshStream.listen((_) {
        debugPrint('🔔 Notification reçue - rechargement des données...');
        _reloadAllData();
      });
      await _loadMaintenanceHistory();

      // Démarrer les mises à jour automatiques
      _startAutoUpdates();

      _clearError();

      // Délai supplémentaire pour afficher l'écran de chargement avec l'image
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      _setError('Erreur lors de l\'initialisation: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Rechargement complet des données après import (méthode publique)
  Future<void> reloadAllData() async {
    await _reloadAllData();
  }

  // Rechargement complet des données après import (méthode privée)
  Future<void> _reloadAllData() async {
    try {
      debugPrint('🔄 Rechargement complet des données après import...');

      // RECHARGER TOUS LES VÉHICULES
      await _loadAllVehicles();
      debugPrint('✅ Tous les véhicules rechargés: ${_allVehicles.length}');

      // RECHARGER LE VÉHICULE ACTUEL
      await _loadCurrentVehicle();
      debugPrint('✅ Véhicule actuel: ${_currentVehicle?.vehicleName}');

      // RECHARGER TOUTES LES DONNÉES
      await _loadMaintenanceItems();
      debugPrint('✅ Entretiens rechargés: ${_maintenanceItems.length}');

      await _loadDocumentItems();
      debugPrint('✅ Documents rechargés: ${_documentItems.length}');

      await _loadMaintenanceHistory();
      debugPrint('✅ Historique rechargé: ${_maintenanceHistory.length}');

      notifyListeners();
      debugPrint('🎉 Rechargement complet terminé - Tous les véhicules et données disponibles');
    } catch (e) {
      debugPrint('❌ Erreur lors du rechargement: $e');
    }
  }

  // Gestion des mises à jour automatiques
  void _startAutoUpdates() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      // Mettre à jour les calculs toutes les 5 minutes
      updateCalculationsAndNotifications();
    });
  }

  void _stopAutoUpdates() {
    _updateTimer?.cancel();
    _updateTimer = null;
  }

  @override
  void dispose() {
    _stopAutoUpdates();
    _refreshSubscription?.cancel();
    super.dispose();
  }

  // Gestion du véhicule
  Future<void> _loadCurrentVehicle() async {
    _currentVehicle = await _databaseService.getDefaultVehicleConfig();
  }

  Future<void> _loadAllVehicles() async {
    _allVehicles = await _databaseService.getAllVehicleConfigs();
  }

  Future<void> updateVehicleKilometers(int kilometers) async {
    if (_currentVehicle == null) return;

    try {
      final updatedVehicle = _currentVehicle!.copyWith(
        currentKilometers: kilometers,
        lastKmUpdate: DateTime.now(),
      );

      await _databaseService.updateVehicleConfig(updatedVehicle);

      // Mettre à jour uniquement les entretiens de ce véhicule
      if (updatedVehicle.id != null) {
        await _databaseService.updateCurrentKilometersForVehicle(kilometers, updatedVehicle.id!);
      }

      _currentVehicle = updatedVehicle;

      // Mettre à jour automatiquement tous les calculs et notifications
      await updateCalculationsAndNotifications();
    } catch (e) {
      _setError('Erreur lors de la mise à jour du kilométrage: $e');
    }
  }

  Future<void> createVehicle(VehicleConfig vehicle) async {
    try {
      final vehicleId = await _databaseService.insertVehicleConfig(vehicle);

      // Créer les données par défaut pour ce nouveau véhicule
      await _databaseService.createDefaultDataForVehicle(vehicleId);

      await _loadAllVehicles(); // Recharger la liste

      // Récupérer le véhicule avec son ID
      final createdVehicle = vehicle.copyWith(id: vehicleId);
      _currentVehicle = createdVehicle;

      // Recharger les données pour ce nouveau véhicule
      await _loadMaintenanceItems();
      await _loadDocumentItems();

      // Initialiser les entretiens et calculs pour le nouveau véhicule
      await updateCalculationsAndNotifications();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la création du véhicule: $e');
    }
  }

  Future<void> updateVehicleConfig(VehicleConfig vehicle) async {
    try {
      await _databaseService.updateVehicleConfig(vehicle);
      await _loadAllVehicles(); // Recharger la liste
      _currentVehicle = vehicle;

      // Mettre à jour les calculs si le kilométrage a changé
      await updateCalculationsAndNotifications();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la mise à jour du véhicule: $e');
    }
  }

  // Changer de véhicule actuel
  Future<void> switchToVehicle(VehicleConfig vehicle) async {
    try {
      // Marquer l'ancien véhicule comme non-défaut
      if (_currentVehicle != null) {
        final oldVehicle = _currentVehicle!.copyWith(isDefault: false);
        await _databaseService.updateVehicleConfig(oldVehicle);
      }

      // Marquer le nouveau véhicule comme défaut
      final newVehicle = vehicle.copyWith(isDefault: true);
      await _databaseService.updateVehicleConfig(newVehicle);

      _currentVehicle = newVehicle;
      await _loadAllVehicles(); // Recharger la liste

      // Recharger les données spécifiques au nouveau véhicule
      await _loadMaintenanceItems();
      await _loadDocumentItems();
      await _loadMaintenanceHistory(); // ⭐ AJOUT CRUCIAL - Recharger l'historique du nouveau véhicule

      // Mettre à jour tous les calculs pour le nouveau véhicule
      await updateCalculationsAndNotifications();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du changement de véhicule: $e');
    }
  }

  // Supprimer un véhicule et toutes ses données associées
  Future<void> deleteVehicle(VehicleConfig vehicle) async {
    try {
      debugPrint('🗑️ Suppression véhicule: ${vehicle.vehicleName} (ID: ${vehicle.id})');

      if (vehicle.id == null) {
        throw Exception('ID du véhicule manquant');
      }

      // Supprimer toutes les données associées au véhicule
      await _databaseService.deleteVehicleAndAllData(vehicle.id!);

      // Recharger la liste des véhicules
      await _loadAllVehicles();
      debugPrint('📊 Véhicules restants après suppression: ${_allVehicles.length}');

      // Si c'était le véhicule actuel, changer pour le premier disponible
      if (_currentVehicle?.id == vehicle.id) {
        if (_allVehicles.isNotEmpty) {
          debugPrint('🔄 Changement vers le premier véhicule disponible');
          await switchToVehicle(_allVehicles.first);
        } else {
          debugPrint('⚠️ Aucun véhicule restant - réinitialisation');
          _currentVehicle = null;
          _maintenanceItems = [];
          _documentItems = [];
          _maintenanceHistory = [];
        }
      }

      notifyListeners();
      debugPrint('✅ Suppression véhicule terminée');
    } catch (e) {
      debugPrint('❌ Erreur suppression véhicule: $e');
      _setError('Erreur lors de la suppression du véhicule: $e');
      rethrow;
    }
  }

  // Gestion des éléments d'entretien
  Future<void> _loadMaintenanceItems() async {
    if (_currentVehicle?.id != null) {
      _maintenanceItems = await _databaseService.getMaintenanceItemsByVehicle(_currentVehicle!.id!);
    } else {
      _maintenanceItems = await _databaseService.getAllMaintenanceItems();
    }

    // Mettre à jour le kilométrage actuel si on a un véhicule
    if (_currentVehicle != null) {
      _maintenanceItems = _maintenanceItems
          .map(
            (item) =>
                item.copyWith(currentKm: _currentVehicle!.currentKilometers),
          )
          .toList();
    }

    // Les notifications sont maintenant générées par _generateIntelligentNotifications()
    // dans updateCalculationsAndNotifications()
  }

  // Méthode pour obtenir TOUS les entretiens (actifs et inactifs) pour les statistiques
  Future<List<MaintenanceItem>> getAllMaintenanceItemsIncludingInactive() async {
    if (_currentVehicle?.id != null) {
      return await _databaseService.getMaintenanceItemsForSettingsByVehicle(_currentVehicle!.id!);
    } else {
      return await _databaseService.getAllMaintenanceItemsForSettings();
    }
  }

  // Gestion des documents
  Future<void> _loadDocumentItems() async {
    if (_currentVehicle?.id != null) {
      _documentItems = await _databaseService.getDocumentItemsByVehicle(_currentVehicle!.id!);
    } else {
      _documentItems = await _databaseService.getAllDocumentItems();
    }
    print('📄 Provider: ${_documentItems.length} documents chargés pour véhicule ${_currentVehicle?.id}');
    for (final doc in _documentItems) {
      print('📋 Provider: ${doc.name} - ID: ${doc.id}');
    }
  }

  Future<void> updateDocumentItem(DocumentItem item) async {
    try {
      // S'assurer que le vehicle_id est préservé
      final itemWithVehicleId = item.copyWith(
        vehicleId: item.vehicleId ?? _currentVehicle?.id,
      );

      await _databaseService.updateDocumentItem(itemWithVehicleId);
      await _loadDocumentItems();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la mise à jour du document: $e');
    }
  }

  Future<void> updateMaintenanceItem(MaintenanceItem item) async {
    try {
      // S'assurer que le vehicle_id est préservé
      final itemWithVehicleId = item.copyWith(
        vehicleId: item.vehicleId ?? _currentVehicle?.id,
      );

      await _databaseService.updateMaintenanceItem(itemWithVehicleId);
      await _loadMaintenanceItems();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la mise à jour de l\'élément: $e');
    }
  }

  Future<void> performMaintenance(
    MaintenanceItem item, {
    int? kilometersAtMaintenance,
    String? notes,
    double? cost,
    String? location,
    String? mechanicName,
    DateTime? maintenanceDate,
  }) async {
    try {
      // Utiliser le kilométrage spécifique ou celui du véhicule par défaut
      final maintenanceKm =
          kilometersAtMaintenance ??
          _currentVehicle?.currentKilometers ??
          item.currentKm;

      // Utiliser la date fournie ou la date actuelle par défaut
      final effectiveDate = maintenanceDate ?? DateTime.now();

      // Créer l'historique
      final history = MaintenanceHistory(
        maintenanceItemId: item.id!,
        kilometersAtMaintenance: maintenanceKm,
        maintenanceDate: effectiveDate,
        notes: notes,
        cost: cost,
        location: location,
        mechanicName: mechanicName,
      );

      await _databaseService.insertMaintenanceHistory(history);

      // Mettre à jour l'élément d'entretien en préservant le vehicle_id
      final updatedItem = item.copyWith(
        lastMaintenanceKm: maintenanceKm,
        lastMaintenanceDate: effectiveDate,
        vehicleId: item.vehicleId ?? _currentVehicle?.id,
      );

      await _databaseService.updateMaintenanceItem(updatedItem);
      await _loadMaintenanceItems();
      await _loadMaintenanceHistory();

      // Mettre à jour automatiquement les calculs et notifications
      await updateCalculationsAndNotifications();
    } catch (e) {
      _setError('Erreur lors de l\'enregistrement de l\'entretien: $e');
    }
  }

  // Gestion de l'historique - MODIFIÉ pour charger seulement le véhicule actuel
  Future<void> _loadMaintenanceHistory() async {
    if (_currentVehicle?.id != null) {
      // Charger seulement l'historique du véhicule actuel
      _maintenanceHistory = await _databaseService.getMaintenanceHistoryByVehicle(_currentVehicle!.id!);
      debugPrint('📊 Provider: ${_maintenanceHistory.length} entrées d\'historique chargées pour véhicule ${_currentVehicle?.id}');
    } else {
      // Fallback : charger tout l'historique
      _maintenanceHistory = await _databaseService.getAllMaintenanceHistory();
      debugPrint('📊 Provider: ${_maintenanceHistory.length} entrées d\'historique chargées (tous véhicules)');
    }
  }

  Future<List<MaintenanceHistory>> getHistoryForItem(int itemId) async {
    return await _databaseService.getMaintenanceHistoryForItem(itemId);
  }

  // Configuration des intervalles
  Future<void> updateMaintenanceInterval(
    MaintenanceItem item,
    int newInterval,
  ) async {
    try {
      final updatedItem = item.copyWith(
        customIntervalKm: newInterval,
        vehicleId: item.vehicleId ?? _currentVehicle?.id,
      );
      await updateMaintenanceItem(updatedItem);
    } catch (e) {
      _setError('Erreur lors de la mise à jour de l\'intervalle: $e');
    }
  }

  Future<void> resetToDefaultInterval(MaintenanceItem item) async {
    try {
      final updatedItem = item.copyWith(
        customIntervalKm: null,
        vehicleId: item.vehicleId ?? _currentVehicle?.id,
      );
      await updateMaintenanceItem(updatedItem);
    } catch (e) {
      _setError('Erreur lors de la réinitialisation de l\'intervalle: $e');
    }
  }

  // Obtenir tous les entretiens (actifs et inactifs) pour les paramètres
  Future<List<MaintenanceItem>> getAllMaintenanceItemsForSettings() async {
    try {
      if (_currentVehicle?.id != null) {
        return await _databaseService.getMaintenanceItemsForSettingsByVehicle(_currentVehicle!.id!);
      } else {
        return await _databaseService.getAllMaintenanceItemsForSettings();
      }
    } catch (e) {
      _setError('Erreur lors du chargement des entretiens: $e');
      return [];
    }
  }

  // Activer/Désactiver un entretien
  Future<void> toggleMaintenanceItemActive(MaintenanceItem item) async {
    try {
      final updatedItem = item.copyWith(
        isActive: !item.isActive,
        vehicleId: item.vehicleId ?? _currentVehicle?.id,
      );
      await _databaseService.updateMaintenanceItem(updatedItem);
      await _loadMaintenanceItems();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la modification de l\'état: $e');
    }
  }

  // Obtenir tous les documents (actifs et inactifs) pour les paramètres
  Future<List<DocumentItem>> getAllDocumentItemsForSettings() async {
    try {
      if (_currentVehicle?.id != null) {
        return await _databaseService.getDocumentItemsForSettingsByVehicle(_currentVehicle!.id!);
      } else {
        return await _databaseService.getAllDocumentItemsForSettings();
      }
    } catch (e) {
      _setError('Erreur lors du chargement des documents: $e');
      return [];
    }
  }

  // Activer/Désactiver un document
  Future<void> toggleDocumentItemActive(DocumentItem item) async {
    try {
      final updatedItem = item.copyWith(
        isActive: !item.isActive,
        vehicleId: item.vehicleId ?? _currentVehicle?.id,
      );
      await _databaseService.updateDocumentItem(updatedItem);
      await _loadDocumentItems();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la modification de l\'état du document: $e');
    }
  }

  // Ajouter un nouveau document
  Future<void> addDocumentItem(DocumentItem item) async {
    try {
      await _databaseService.insertDocumentItem(item);
      await _loadDocumentItems();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de l\'ajout du document: $e');
    }
  }

  // Gestion des erreurs et du loading
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // Méthodes pour l'état global de tous les véhicules
  Future<void> loadAllVehiclesData() async {
    try {
      _setLoading(true);
      await _loadAllVehicles();
      _setLoading(false);
    } catch (e) {
      _setError('Erreur lors du chargement des véhicules: $e');
      _setLoading(false);
    }
  }

  Future<List<MaintenanceItem>> getMaintenanceItemsForVehicle(int vehicleId) async {
    try {
      return await _databaseService.getMaintenanceItemsByVehicle(vehicleId);
    } catch (e) {
      debugPrint('Erreur lors du chargement des entretiens pour véhicule $vehicleId: $e');
      return [];
    }
  }

  Future<List<DocumentItem>> getDocumentItemsForVehicle(int vehicleId) async {
    try {
      return await _databaseService.getDocumentItemsByVehicle(vehicleId);
    } catch (e) {
      debugPrint('Erreur lors du chargement des documents pour véhicule $vehicleId: $e');
      return [];
    }
  }

  Map<String, dynamic> getMaintenanceItemStatusForVehicle(
    MaintenanceItem item,
    VehicleConfig vehicle,
  ) {
    final kmSinceLastMaintenance = vehicle.currentKilometers - item.lastMaintenanceKm;
    final intervalKm = item.customIntervalKm ?? item.defaultIntervalKm;
    final remainingKm = intervalKm - kmSinceLastMaintenance;

    Color statusColor;
    if (remainingKm <= 0) {
      statusColor = Colors.red; // En retard
    } else if (remainingKm <= intervalKm * 0.1) {
      statusColor = Colors.red; // Critique - reste ≤ 10%
    } else if (remainingKm <= intervalKm * 0.2) {
      statusColor = Colors.orange; // Proche - reste ≤ 20%
    } else {
      statusColor = Colors.green; // OK
    }

    return {
      'remainingKm': remainingKm,
      'statusColor': statusColor,
      'wearPercentage': ((kmSinceLastMaintenance / intervalKm) * 100).clamp(0, 100),
    };
  }

  /// Méthode temporaire pour recréer la base de données
  Future<void> recreateDatabase() async {
    try {
      await _databaseService.recreateDatabase();
      // Réinitialiser après recréation
      await initialize();
    } catch (e) {
      _setError('Erreur lors de la recréation de la base: $e');
      rethrow;
    }
  }

  // Calculs automatiques et prédictions

  /// Calcule le pourcentage d'usure pour un élément d'entretien
  double calculateWearPercentage(MaintenanceItem item) {
    if (_currentVehicle == null) return 0.0;

    final currentKm = _currentVehicle!.currentKilometers;
    final lastMaintenanceKm = item.lastMaintenanceKm;
    final intervalKm = item.customIntervalKm ?? item.defaultIntervalKm;

    if (intervalKm <= 0) return 0.0;

    // Vérification de cohérence : le kilométrage actuel doit être >= au dernier entretien
    if (currentKm < lastMaintenanceKm) {
      // Cas d'erreur : kilométrage incohérent
      return 0.0;
    }

    final kmSinceLastMaintenance = currentKm - lastMaintenanceKm;
    final wearPercentage = (kmSinceLastMaintenance / intervalKm) * 100;

    return wearPercentage.clamp(
      0.0,
      200.0,
    ); // Permettre jusqu'à 200% pour les cas très en retard
  }

  /// Obtient le kilométrage actuel du véhicule (source de vérité)
  int get currentVehicleKilometers => _currentVehicle?.currentKilometers ?? 0;

  /// Compare le kilométrage actuel avec le dernier entretien
  Map<String, dynamic> compareKilometersWithLastMaintenance(
    MaintenanceItem item,
  ) {
    if (_currentVehicle == null) {
      return {
        'isValid': false,
        'error': 'Aucun véhicule configuré',
        'currentKm': 0,
        'lastMaintenanceKm': item.lastMaintenanceKm,
        'difference': 0,
      };
    }

    final currentKm = _currentVehicle!.currentKilometers;
    final lastMaintenanceKm = item.lastMaintenanceKm;
    final difference = currentKm - lastMaintenanceKm;

    return {
      'isValid': currentKm >= lastMaintenanceKm,
      'error': currentKm < lastMaintenanceKm
          ? 'Kilométrage actuel ($currentKm km) inférieur au dernier entretien ($lastMaintenanceKm km)'
          : null,
      'currentKm': currentKm,
      'lastMaintenanceKm': lastMaintenanceKm,
      'difference': difference,
      'isCoherent': difference >= 0,
    };
  }

  /// Calcule les kilomètres restants avant le prochain entretien
  int calculateRemainingKilometers(MaintenanceItem item) {
    if (_currentVehicle == null) return 0;

    final currentKm = _currentVehicle!.currentKilometers;
    final lastMaintenanceKm = item.lastMaintenanceKm;
    final intervalKm = item.customIntervalKm ?? item.defaultIntervalKm;

    // Vérification de cohérence
    if (currentKm < lastMaintenanceKm) {
      // Kilométrage incohérent, retourner l'intervalle complet
      return intervalKm;
    }

    final nextMaintenanceKm = lastMaintenanceKm + intervalKm;
    final remainingKm = nextMaintenanceKm - currentKm;

    return remainingKm > 0 ? remainingKm : 0;
  }

  /// Obtient des informations détaillées sur l'état d'un entretien
  Map<String, dynamic> getMaintenanceItemStatus(MaintenanceItem item) {
    final comparison = compareKilometersWithLastMaintenance(item);
    final wearPercentage = calculateWearPercentage(item);
    final remainingKm = calculateRemainingKilometers(item);
    final estimatedDate = calculateEstimatedMaintenanceDate(item);

    String status;
    Color statusColor;
    IconData statusIcon;

    if (!comparison['isCoherent']) {
      status = 'ERREUR';
      statusColor = Colors.purple;
      statusIcon = Icons.error;
    } else if (wearPercentage >= 100) {
      status = 'URGENT';
      statusColor = Colors.red;
      statusIcon = Icons.warning;
    } else if (wearPercentage >= 80) {
      status = 'BIENTÔT';
      statusColor = Colors.orange;
      statusIcon = Icons.schedule;
    } else if (wearPercentage >= 60) {
      status = 'PLANIFIER';
      statusColor = Colors.blue;
      statusIcon = Icons.event;
    } else {
      status = 'OK';
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    }

    return {
      'status': status,
      'statusColor': statusColor,
      'statusIcon': statusIcon,
      'wearPercentage': wearPercentage,
      'remainingKm': remainingKm,
      'estimatedDate': estimatedDate,
      'comparison': comparison,
      'isOverdue': wearPercentage >= 100,
      'isApproaching': wearPercentage >= 80 && wearPercentage < 100,
      'needsPlanning': wearPercentage >= 60 && wearPercentage < 80,
    };
  }

  /// Calcule la date estimée du prochain entretien basée sur l'usage moyen
  DateTime? calculateEstimatedMaintenanceDate(MaintenanceItem item) {
    if (_currentVehicle == null) return null;

    final remainingKm = calculateRemainingKilometers(item);
    if (remainingKm <= 0) return DateTime.now();

    // Calculer l'usage moyen par jour
    final daysSinceLastUpdate = DateTime.now()
        .difference(_currentVehicle!.lastKmUpdate)
        .inDays;
    if (daysSinceLastUpdate <= 0) return null;

    final kmSinceLastUpdate =
        _currentVehicle!.currentKilometers - item.lastMaintenanceKm;
    final averageKmPerDay = kmSinceLastUpdate / daysSinceLastUpdate;

    if (averageKmPerDay <= 0) return null;

    final daysUntilMaintenance = (remainingKm / averageKmPerDay).round();
    return DateTime.now().add(Duration(days: daysUntilMaintenance));
  }

  /// Met à jour automatiquement tous les calculs et notifications
  Future<void> updateCalculationsAndNotifications() async {
    if (_currentVehicle == null) return;

    try {
      // Recalculer tous les éléments d'entretien
      await _loadMaintenanceItems();

      // Générer les nouvelles notifications
      _generateIntelligentNotifications();

      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la mise à jour des calculs: $e');
    }
  }

  /// Génère des notifications intelligentes basées sur les calculs
  void _generateIntelligentNotifications() {
    _notificationService.clearAllNotifications();

    for (final item in _maintenanceItems) {
      // Ignorer les éléments inactifs
      if (!item.isActive) continue;

      // Ignorer complètement les opérations de type "Autre"
      if (item.category.toLowerCase() == 'autre') {
        continue; // Passer à l'élément suivant sans générer de notification
      }

      // Pour les opérations avec intervalles définis
      final wearPercentage = calculateWearPercentage(item);
      final remainingKm = calculateRemainingKilometers(item);
      final estimatedDate = calculateEstimatedMaintenanceDate(item);

      // Notification urgente (100% ou plus d'usure)
      if (wearPercentage >= 100) {
        _notificationService.addNotification(
          MaintenanceNotification(
            id: 'urgent_${item.id}',
            title: '🚨 Entretien urgent requis',
            message:
                '${item.name} doit être effectué immédiatement (${wearPercentage.toInt()}% d\'usure)',
            type: NotificationType.urgent,
            maintenanceItem: item,
            createdAt: DateTime.now(),
          ),
        );
      }
      // Notification d'approche (80-99% d'usure)
      else if (wearPercentage >= 80) {
        final daysUntil = estimatedDate != null
            ? estimatedDate.difference(DateTime.now()).inDays
            : null;

        _notificationService.addNotification(
          MaintenanceNotification(
            id: 'approaching_${item.id}',
            title: '⚠️ Entretien bientôt nécessaire',
            message:
                '${item.name} dans ${remainingKm}km'
                '${daysUntil != null ? ' (≈ $daysUntil jours)' : ''}'
                ' (${wearPercentage.toInt()}% d\'usure)',
            type: NotificationType.reminder,
            maintenanceItem: item,
            createdAt: DateTime.now(),
          ),
        );
      }
      // Notification de rappel (60-79% d'usure)
      else if (wearPercentage >= 60) {
        _notificationService.addNotification(
          MaintenanceNotification(
            id: 'reminder_${item.id}',
            title: '📅 Planifier l\'entretien',
            message:
                '${item.name} dans ${remainingKm}km (${wearPercentage.toInt()}% d\'usure)',
            type: NotificationType.reminder,
            maintenanceItem: item,
            createdAt: DateTime.now(),
          ),
        );
      }
    }
  }

  /// Obtient les statistiques détaillées
  Map<String, dynamic> getDetailedStatistics() {
    if (_currentVehicle == null) {
      return {
        'totalItems': 0,
        'urgent': 0,
        'approaching': 0,
        'ok': 0,
        'averageWear': 0.0,
        'nextMaintenanceKm': null,
        'nextMaintenanceItem': null,
      };
    }

    final urgent = _maintenanceItems
        .where((item) => calculateWearPercentage(item) >= 100)
        .length;
    final approaching = _maintenanceItems.where((item) {
      final wear = calculateWearPercentage(item);
      return wear >= 80 && wear < 100;
    }).length;
    final ok = _maintenanceItems.length - urgent - approaching;

    final averageWear = _maintenanceItems.isEmpty
        ? 0.0
        : _maintenanceItems
                  .map((item) => calculateWearPercentage(item))
                  .reduce((a, b) => a + b) /
              _maintenanceItems.length;

    // Trouver l'entretien le plus urgent (plus fort pourcentage d'usure)
    MaintenanceItem? nextItem;
    int? nextKm;
    double highestWearPercentage = -1;

    for (final item in _maintenanceItems) {
      final wearPercentage = calculateWearPercentage(item);
      final remainingKm = calculateRemainingKilometers(item);

      // Prioriser par pourcentage d'usure (le plus urgent en premier)
      if (wearPercentage > highestWearPercentage) {
        highestWearPercentage = wearPercentage;
        nextKm = remainingKm;
        nextItem = item;
      }
    }

    return {
      'totalItems': _maintenanceItems.length,
      'urgent': urgent,
      'approaching': approaching,
      'ok': ok,
      'averageWear': averageWear,
      'nextMaintenanceKm': nextKm,
      'nextMaintenanceItem': nextItem,
    };
  }

  // Méthodes de calcul des coûts

  /// Calculer le coût total des entretiens effectués ce mois
  double calculateMonthlyMaintenanceCost() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    return _maintenanceHistory
        .where(
          (history) =>
              history.maintenanceDate.isAfter(startOfMonth) &&
              history.maintenanceDate.isBefore(endOfMonth),
        )
        .map((history) => history.cost ?? 0.0)
        .fold(0.0, (sum, cost) => sum + cost);
  }

  /// Calculer le coût total des entretiens effectués cette année
  double calculateYearlyMaintenanceCost() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);

    return _maintenanceHistory
        .where(
          (history) =>
              history.maintenanceDate.isAfter(startOfYear) &&
              history.maintenanceDate.isBefore(endOfYear),
        )
        .map((history) => history.cost ?? 0.0)
        .fold(0.0, (sum, cost) => sum + cost);
  }

  /// Calculer les coûts par catégorie (basé sur l'historique réel)
  Map<String, double> calculateCostsByCategory() {
    final Map<String, double> costsByCategory = {};

    for (final history in _maintenanceHistory) {
      if (history.cost != null) {
        final maintenanceItem = _maintenanceItems
            .where((item) => item.id == history.maintenanceItemId)
            .firstOrNull;

        if (maintenanceItem != null) {
          final category = maintenanceItem.category;
          costsByCategory[category] =
              (costsByCategory[category] ?? 0.0) + history.cost!;
        }
      }
    }

    return costsByCategory;
  }

  /// Obtenir les statistiques de coûts détaillées
  Map<String, dynamic> getCostStatistics() {
    return {
      'monthlyActual': calculateMonthlyMaintenanceCost(),
      'yearlyActual': calculateYearlyMaintenanceCost(),
      'costsByCategory': calculateCostsByCategory(),
    };
  }

  // Gestion de l'historique
  Future<void> addMaintenanceHistory(MaintenanceHistory history) async {
    try {
      await _databaseService.insertMaintenanceHistory(history);
      await _loadMaintenanceHistory();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de l\'ajout de l\'historique: $e');
      rethrow;
    }
  }

  // Ajouter un élément d'entretien personnalisé
  Future<int> addCustomMaintenanceItem(MaintenanceItem item) async {
    try {
      final itemId = await _databaseService.insertMaintenanceItem(item);
      await _loadMaintenanceItems();
      notifyListeners();
      return itemId;
    } catch (e) {
      _setError('Erreur lors de l\'ajout de l\'élément personnalisé: $e');
      rethrow;
    }
  }

  // Ajouter un historique d'entretien personnalisé temporaire (sans l'ajouter à la liste déroulante)
  Future<void> addCustomMaintenanceHistoryOnly(
    String operationName,
    double cost,
    int kilometers,
    DateTime date,
  ) async {
    try {
      // Créer un MaintenanceItem temporaire pour l'opération personnalisée
      // Utiliser un préfixe spécial pour identifier les entretiens temporaires dans la liste déroulante
      final customItem = MaintenanceItem(
        name: '[CUSTOM] $operationName', // Préfixe pour identifier les entretiens personnalisés
        description: 'Opération personnalisée',
        iconPath: 'assets/icons/wrench.png',
        defaultIntervalKm: 0, // Pas d'intervalle pour les opérations personnalisées
        currentKm: kilometers,
        lastMaintenanceKm: kilometers,
        lastMaintenanceDate: date,
        category: 'Autre',
        vehicleId: _currentVehicle?.id,
        isActive: true, // Actif pour apparaître dans les statistiques
      );

      // Insérer l'item temporaire dans la base de données
      final itemId = await _databaseService.insertMaintenanceItem(customItem);

      // Créer l'historique avec l'ID de l'item créé
      final history = MaintenanceHistory(
        maintenanceItemId: itemId,
        kilometersAtMaintenance: kilometers,
        maintenanceDate: date,
        cost: cost,
      );

      await _databaseService.insertMaintenanceHistory(history);

      // Recharger les données pour que l'entretien apparaisse dans les statistiques et l'historique
      await _loadMaintenanceItems(); // Nécessaire pour les calculs de coûts
      await _loadMaintenanceHistory();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de l\'ajout de l\'historique personnalisé: $e');
      rethrow;
    }
  }

  Future<void> deleteMaintenanceHistory(int historyId) async {
    try {
      await _databaseService.deleteMaintenanceHistory(historyId);
      await _loadMaintenanceHistory();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la suppression de l\'historique: $e');
      rethrow;
    }
  }

  /// Met à jour complètement un historique d'entretien
  Future<void> updateMaintenanceHistory(
    MaintenanceHistory updatedHistory,
  ) async {
    try {
      // Supprimer l'ancien historique
      await _databaseService.deleteMaintenanceHistory(updatedHistory.id!);

      // Créer le nouvel historique sans ID pour qu'il soit auto-généré
      final newHistory = MaintenanceHistory(
        maintenanceItemId: updatedHistory.maintenanceItemId,
        kilometersAtMaintenance: updatedHistory.kilometersAtMaintenance,
        maintenanceDate: updatedHistory.maintenanceDate,
        notes: updatedHistory.notes,
        cost: updatedHistory.cost,
        location: updatedHistory.location,
        mechanicName: updatedHistory.mechanicName,
        photos: updatedHistory.photos,
        isCompleted: updatedHistory.isCompleted,
      );

      // Insérer le nouvel historique
      await _databaseService.insertMaintenanceHistory(newHistory);
      await _loadMaintenanceHistory();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la mise à jour de l\'historique: $e');
      rethrow;
    }
  }

  /// Met à jour le kilométrage d'un entretien dans l'historique
  Future<void> updateMaintenanceHistoryKilometers(
    int historyId,
    int newKilometers,
  ) async {
    try {
      // Trouver l'historique à modifier pour obtenir l'ID de l'item
      final historyToUpdate = _maintenanceHistory.firstWhere(
        (history) => history.id == historyId,
      );

      // Mettre à jour l'historique
      await _databaseService.updateMaintenanceHistoryKilometers(
        historyId,
        newKilometers,
      );

      // Mettre à jour aussi l'item correspondant
      final itemToUpdate = _maintenanceItems.firstWhere(
        (item) => item.id == historyToUpdate.maintenanceItemId,
      );

      final updatedItem = itemToUpdate.copyWith(
        lastMaintenanceKm: newKilometers,
        lastMaintenanceDate: historyToUpdate.maintenanceDate,
        vehicleId: itemToUpdate.vehicleId ?? _currentVehicle?.id,
      );

      await _databaseService.updateMaintenanceItem(updatedItem);

      // Recharger toutes les données
      await _loadMaintenanceItems();
      await _loadMaintenanceHistory();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la mise à jour du kilométrage: $e');
      rethrow;
    }
  }

  // Méthodes utilitaires
  Future<void> resetAllData() async {
    try {
      await _databaseService.resetDatabase();
      await initialize();
    } catch (e) {
      _setError('Erreur lors de la réinitialisation: $e');
    }
  }

  /// Gère la première installation de l'application
  Future<void> _handleFirstInstallation() async {
    try {
      // Vérifier si c'est la première installation via SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = prefs.getBool('is_first_launch') ?? true;

      if (isFirstLaunch) {
        debugPrint('🆕 PREMIÈRE INSTALLATION DÉTECTÉE');

        // Créer un véhicule par défaut
        await _createDefaultVehicleForFirstInstall();

        // Marquer comme non-première installation
        await prefs.setBool('is_first_launch', false);

        debugPrint('✅ Configuration première installation terminée');
      } else {
        debugPrint('🔄 Installation existante détectée');
      }
    } catch (e) {
      debugPrint('❌ Erreur gestion première installation: $e');
      // Continuer normalement en cas d'erreur
    }
  }

  /// Crée un véhicule par défaut pour la première installation
  Future<void> _createDefaultVehicleForFirstInstall() async {
    try {
      debugPrint('🚗 Création véhicule par défaut...');

      // Vérifier s'il y a déjà des véhicules
      final existingVehicles = await _databaseService.getAllVehicleConfigs();

      if (existingVehicles.isEmpty) {
        // Créer un véhicule par défaut
        final defaultVehicle = VehicleConfig(
          vehicleName: 'Mon Véhicule',
          brand: 'Marque',
          model: 'Modèle',
          year: DateTime.now().year,
          currentKilometers: 0,
          lastKmUpdate: DateTime.now(),
          isDefault: true,
        );

        // Ajouter le véhicule à la base
        final vehicleId = await _databaseService.insertVehicleConfig(defaultVehicle);

        // Créer les données par défaut (intervalles actifs) pour ce véhicule
        await _databaseService.createDefaultDataForVehicle(vehicleId);

        debugPrint('✅ Véhicule par défaut créé avec ID: $vehicleId');
        debugPrint('✅ Tous les intervalles d\'entretien activés');
      } else {
        debugPrint('ℹ️ Véhicules existants trouvés, pas de création');
      }
    } catch (e) {
      debugPrint('❌ Erreur création véhicule par défaut: $e');
    }
  }
}
