import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalizationService extends ChangeNotifier {
  static final LocalizationService _instance = LocalizationService._internal();
  factory LocalizationService() => _instance;
  LocalizationService._internal();

  static const String _languageKey = 'selected_language';
  
  // Langues supportées
  static const List<Locale> supportedLocales = [
    Locale('fr', 'FR'), // Français
    Locale('ar', 'SA'), // Arabe
  ];

  Locale _currentLocale = const Locale('fr', 'FR');
  
  Locale get currentLocale => _currentLocale;
  
  bool get isRTL => _currentLocale.languageCode == 'ar';
  
  String get languageCode => _currentLocale.languageCode;

  /// Initialiser le service de localisation
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguage = prefs.getString(_languageKey);
    
    if (savedLanguage != null) {
      // Trouver la locale correspondante
      final locale = supportedLocales.firstWhere(
        (locale) => locale.languageCode == savedLanguage,
        orElse: () => const Locale('fr', 'FR'),
      );
      _currentLocale = locale;
    }
    
    notifyListeners();
  }

  /// Changer la langue
  Future<void> changeLanguage(String languageCode) async {
    final locale = supportedLocales.firstWhere(
      (locale) => locale.languageCode == languageCode,
      orElse: () => const Locale('fr', 'FR'),
    );
    
    if (locale != _currentLocale) {
      _currentLocale = locale;
      
      // Sauvegarder la préférence
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      
      notifyListeners();
    }
  }

  /// Obtenir le nom de la langue actuelle
  String getCurrentLanguageName() {
    switch (_currentLocale.languageCode) {
      case 'fr':
        return 'Français';
      case 'ar':
        return 'العربية';
      default:
        return 'Français';
    }
  }

  /// Obtenir toutes les langues disponibles
  List<Map<String, String>> getAvailableLanguages() {
    return [
      {'code': 'fr', 'name': 'Français', 'nativeName': 'Français'},
      {'code': 'ar', 'name': 'Arabic', 'nativeName': 'العربية'},
    ];
  }
}