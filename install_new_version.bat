@echo off
echo ========================================
echo   INSTALLATION NOUVELLE VERSION
echo ========================================
echo.

REM Vérifier si l'APK debug existe
if not exist "build\app\outputs\flutter-apk\app-debug.apk" (
    echo ❌ ERREUR: APK debug non trouvé
    echo    Construisez d'abord l'APK avec: flutter build apk --debug
    pause
    exit /b 1
)

echo ✅ APK debug trouvé: build\app\outputs\flutter-apk\app-debug.apk
echo.

echo 📱 INSTRUCTIONS POUR FORCER L'INSTALLATION:
echo.
echo 🔧 MÉTHODE 1 - Désinstallation manuelle (RECOMMANDÉE):
echo    1. Sur votre téléphone, allez dans Paramètres > Applications
echo    2. Trouvez "MyCar Maintenance" ou "Carosti"
echo    3. Appuyez sur "Désinstaller"
echo    4. Confirmez la désinstallation
echo    5. Transférez l'APK sur votre téléphone
echo    6. Installez l'APK (activez "Sources inconnues" si nécessaire)
echo.

echo 🔧 MÉTHODE 2 - Via ADB (si ADB est configuré):
echo    1. Connectez votre téléphone en mode développeur
echo    2. Activez le débogage USB
echo    3. Exécutez les commandes suivantes:
echo.
echo    adb devices
echo    adb uninstall com.carosti.app
echo    adb install build\app\outputs\flutter-apk\app-debug.apk
echo.

echo 🔧 MÉTHODE 3 - Installation directe avec remplacement:
echo    1. Transférez l'APK sur votre téléphone
echo    2. Ouvrez l'APK depuis le gestionnaire de fichiers
echo    3. Si Android refuse l'installation, allez dans:
echo       Paramètres > Sécurité > Sources inconnues (activez)
echo    4. Réessayez l'installation
echo.

echo 📋 INFORMATIONS IMPORTANTES:
echo    - Package: com.carosti.app
echo    - Nouvelle signature: google-play-release.jks
echo    - Ancienne signature: carosti-release-key.jks
echo    - Les données de l'app seront perdues lors de la désinstallation
echo.

set /p CHOICE="Voulez-vous ouvrir le dossier contenant l'APK? (o/N): "
if /i "%CHOICE%"=="o" (
    echo 📂 Ouverture du dossier...
    explorer "build\app\outputs\flutter-apk\"
)

echo.
echo 🚀 APK prêt pour l'installation !
echo    Fichier: build\app\outputs\flutter-apk\app-debug.apk
echo    Taille: 
for %%A in ("build\app\outputs\flutter-apk\app-debug.apk") do echo    %%~zA bytes
echo.

echo 💡 CONSEIL: Sauvegardez vos données avant la désinstallation !
echo.
pause
