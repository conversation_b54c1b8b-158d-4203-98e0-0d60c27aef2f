import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/notification_provider.dart';
import '../providers/maintenance_provider.dart';
import '../widgets/notification_badge.dart';
import '../services/app_state_service.dart';
import 'notification_settings_screen.dart';
import 'kilometer_input_screen.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  void initState() {
    super.initState();
    // Marquer la page comme visitée
    AppStateService.markNotificationPageVisited();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshNotifications();
    });
  }

  void _refreshNotifications() {
    final notificationProvider = context.read<NotificationProvider>();
    final maintenanceProvider = context.read<MaintenanceProvider>();

    if (maintenanceProvider.currentVehicle != null) {
      // Générer les prédictions
      notificationProvider.generatePredictions(
        vehicle: maintenanceProvider.currentVehicle!,
        maintenanceItems: maintenanceProvider.maintenanceItems,
      );

      // Générer les alertes
      notificationProvider.generateAlerts(
        vehicle: maintenanceProvider.currentVehicle!,
        maintenanceItems: maintenanceProvider.maintenanceItems,
        documentItems: maintenanceProvider.documentItems,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          Consumer<NotificationProvider>(
            builder: (context, notificationProvider, child) {
              if (notificationProvider.alerts.isNotEmpty) {
                return IconButton(
                  onPressed: () {
                    _showClearAllDialog(context);
                  },
                  icon: const Icon(Icons.clear_all),
                  tooltip: 'Effacer toutes les notifications',
                );
              }
              return const SizedBox.shrink();
            },
          ),
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationSettingsScreen(),
                ),
              );
            },
            icon: const Icon(Icons.settings),
            tooltip: 'Paramètres de notification',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _refreshNotifications();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusCard(),
              const SizedBox(height: 16),
              _buildNotificationsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Consumer<NotificationProvider>(
      builder: (context, notificationProvider, child) {
        final totalAlerts = notificationProvider.totalAlertCount;
        final urgentCount = notificationProvider.getAlertCount(NotificationAlertType.urgent);
        final warningCount = notificationProvider.getAlertCount(NotificationAlertType.warning);
        final reminderCount = notificationProvider.getAlertCount(NotificationAlertType.reminder);

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      totalAlerts > 0 ? MdiIcons.bellAlert : MdiIcons.bellCheck,
                      color: totalAlerts > 0 
                          ? (urgentCount > 0 ? Colors.red : Colors.orange)
                          : Colors.green,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            totalAlerts > 0 
                                ? '$totalAlerts notification${totalAlerts > 1 ? 's' : ''}'
                                : 'Aucune notification',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            totalAlerts > 0 
                                ? 'Vérifiez vos alertes ci-dessous'
                                : 'Tout est à jour !',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (totalAlerts > 0) ...[
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      if (urgentCount > 0) ...[
                        _buildStatusChip('Urgent', urgentCount, Colors.red),
                        const SizedBox(width: 8),
                      ],
                      if (warningCount > 0) ...[
                        _buildStatusChip('Attention', warningCount, Colors.orange),
                        const SizedBox(width: 8),
                      ],
                      if (reminderCount > 0) ...[
                        _buildStatusChip('Rappel', reminderCount, Colors.blue),
                      ],
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            '$count $label',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              MdiIcons.bellOutline,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Notifications Actives',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        const NotificationPanel(),
      ],
    );
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Effacer toutes les notifications'),
        content: const Text(
          'Êtes-vous sûr de vouloir effacer toutes les notifications ? '
          'Cette action ne peut pas être annulée.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<NotificationProvider>().clearAllAlerts();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Toutes les notifications ont été effacées'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Effacer'),
          ),
        ],
      ),
    );
  }
}
