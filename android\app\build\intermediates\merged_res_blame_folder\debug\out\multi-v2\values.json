{"logs": [{"outputFile": "com.carosti.app-mergeDebugResources-50:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f200f4a28092d3753dbbadd5663700bb\\transformed\\jetified-play-services-base-18.0.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "453,454,455,456,457,458,459,460,800,801,802,803,804,805,806,807,809,810,811,812,813,814,815,816,817,4408,4982", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20196,20286,20366,20456,20546,20626,20707,20787,42311,42416,42597,42722,42829,43009,43132,43248,43518,43706,43811,43992,44117,44292,44440,44503,44565,229950,250252", "endLines": "453,454,455,456,457,458,459,460,800,801,802,803,804,805,806,807,809,810,811,812,813,814,815,816,817,4420,5000", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "20281,20361,20451,20541,20621,20702,20782,20862,42411,42592,42717,42824,43004,43127,43243,43346,43701,43806,43987,44112,44287,44435,44498,44560,44639,230260,250664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1e115093ecfdcf00d62fda419f1b05b4\\transformed\\jetified-play-services-ads-23.6.0\\res\\values\\values.xml", "from": {"startLines": "5,7,10,13,16,19,21,23,25,27,29,31,33,35,37,39,41,42,43,44,45,46,47,48", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "167,224,373,620,869,1145,1284,1441,1653,1857,2085,2309,2572,2743,2926,3145,3330,3368,3441,3475,3510,3559,3623,3658", "endLines": "5,7,12,15,18,20,22,24,26,28,30,32,34,36,38,40,41,42,43,44,45,46,47,50", "endColumns": "55,45,11,11,11,20,27,51,19,86,68,62,17,24,81,48,37,72,33,34,48,63,34,11", "endOffsets": "222,269,619,868,1144,1283,1440,1652,1856,2084,2308,2571,2742,2925,3144,3329,3367,3440,3474,3509,3558,3622,3657,3822"}, "to": {"startLines": "715,716,824,827,830,834,836,838,840,842,844,846,848,850,852,854,859,860,861,862,863,864,865,871", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "37165,37225,45022,45278,45536,45865,46008,46169,46385,46593,46825,47053,47320,47495,47682,47905,48234,48276,48353,48391,48430,48483,48551,48951", "endLines": "715,716,826,829,832,835,837,839,841,843,845,847,849,851,853,855,859,860,861,862,863,864,865,873", "endColumns": "59,49,11,11,11,20,27,51,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "37220,37270,45273,45531,45816,46003,46164,46380,46588,46820,47048,47315,47490,47677,47900,48089,48271,48348,48386,48425,48478,48546,48585,49115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d2a2fbb10f1c4f98ad3b7125d70ab54f\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "39,2553,3885,3891", "startColumns": "4,4,4,4", "startOffsets": "1818,160248,208825,209036", "endLines": "39,2555,3890,3974", "endColumns": "60,12,24,24", "endOffsets": "1874,160388,209031,213547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a796bc1946da6b7a95ec63ccd9e2a134\\transformed\\jetified-play-services-ads-lite-23.6.0\\res\\values\\values.xml", "from": {"startLines": "4,14", "startColumns": "0,0", "startOffsets": "167,661", "endLines": "11,20", "endColumns": "8,20", "endOffsets": "560,836"}, "to": {"startLines": "2436,2671", "startColumns": "4,4", "startOffsets": "151052,164269", "endLines": "2443,2677", "endColumns": "8,20", "endOffsets": "151445,164444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4116675e28f344e3bc6e9bb9e74eaffc\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "707,726,755,4084,4089", "startColumns": "4,4,4,4,4", "startOffsets": "36810,37715,39235,218111,218281", "endLines": "707,726,755,4088,4092", "endColumns": "56,64,63,24,24", "endOffsets": "36862,37775,39294,218276,218425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1ac570f9a9ce8a2badd3127fcdbc117\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "303,433,434,451,452,494,495,597,598,599,600,601,602,603,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,712,713,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,763,793,794,795,796,797,798,799,867,2389,2390,2394,2395,2399,2551,2552,3215,3250,4030,4063,4093,4126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13226,18696,18768,20065,20130,22756,22825,29818,29888,29956,30028,30098,30159,30233,32558,32619,32680,32742,32806,32868,32929,32997,33097,33157,33223,33296,33365,33422,33474,34422,34494,34570,34635,34694,34753,34813,34873,34933,34993,35053,35113,35173,35233,35293,35353,35412,35472,35532,35592,35652,35712,35772,35832,35892,35952,36012,36071,36131,36191,36250,36309,36368,36427,36486,37054,37089,37826,37881,37944,37999,38057,38115,38176,38239,38296,38347,38397,38458,38515,38581,38615,38650,39736,41800,41867,41939,42008,42077,42151,42223,48643,147658,147775,147976,148086,148287,160109,160181,181945,183486,215699,217430,218430,219112", "endLines": "303,433,434,451,452,494,495,597,598,599,600,601,602,603,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,712,713,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,763,793,794,795,796,797,798,799,867,2389,2393,2394,2398,2399,2551,2552,3220,3259,4062,4083,4125,4131", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "13281,18763,18851,20125,20191,22820,22883,29883,29951,30023,30093,30154,30228,30301,32614,32675,32737,32801,32863,32924,32992,33092,33152,33218,33291,33360,33417,33469,33531,34489,34565,34630,34689,34748,34808,34868,34928,34988,35048,35108,35168,35228,35288,35348,35407,35467,35527,35587,35647,35707,35767,35827,35887,35947,36007,36066,36126,36186,36245,36304,36363,36422,36481,36540,37084,37119,37876,37939,37994,38052,38110,38171,38234,38291,38342,38392,38453,38510,38576,38610,38645,38680,39801,41862,41934,42003,42072,42146,42218,42306,48709,147770,147971,148081,148282,148411,160176,160243,182143,183782,217425,218106,219107,219274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3a8d89e27875daab33c524cac617c2ba\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "422,423,424,425", "startColumns": "4,4,4,4", "startOffsets": "17935,18000,18070,18134", "endColumns": "64,69,63,60", "endOffsets": "17995,18065,18129,18190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a82432f68aaeb6fdcda413b8f596f847\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "754", "startColumns": "4", "startOffsets": "39185", "endColumns": "49", "endOffsets": "39230"}}, {"source": "C:\\Users\\<USER>\\Desktop\\AMEUR APP\\mycar_maintenance\\build\\google_mobile_ads\\intermediates\\packaged_res\\debug\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,102,146,189,232,276,322,364,426,490,534,593,655,710,768,826,880,930,1008,1068,1158,1245,1289,1331,1390,1437,1514,1565,1616", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,32", "endColumns": "46,43,42,42,43,45,41,61,63,43,58,61,54,57,57,53,49,77,59,89,86,43,41,58,46,76,50,50,22", "endOffsets": "97,141,184,227,271,317,359,421,485,529,588,650,705,763,821,875,925,1003,1063,1153,1240,1284,1326,1385,1432,1509,1560,1611,1734"}, "to": {"startLines": "469,470,471,472,473,474,475,476,477,478,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,5179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21413,21460,21504,21547,21590,21634,21680,21722,21784,21848,30642,30701,30763,30818,30876,30934,30988,31038,31116,31176,31266,31353,31397,31439,31498,31545,31622,31673,257463", "endLines": "469,470,471,472,473,474,475,476,477,478,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,5181", "endColumns": "46,43,42,42,43,45,41,61,63,43,58,61,54,57,57,53,49,77,59,89,86,43,41,58,46,76,50,50,22", "endOffsets": "21455,21499,21542,21585,21629,21675,21717,21779,21843,21887,30696,30758,30813,30871,30929,30983,31033,31111,31171,31261,31348,31392,31434,31493,31540,31617,31668,31719,257581"}}, {"source": "C:\\Users\\<USER>\\Desktop\\AMEUR APP\\mycar_maintenance\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "481", "startColumns": "4", "startOffsets": "22027", "endColumns": "56", "endOffsets": "22079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\80b747fd0bf560516ae3aa3beeee4d5c\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "760,808", "startColumns": "4,4", "startOffsets": "39542,43351", "endColumns": "67,166", "endOffsets": "39605,43513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7fb97e251b4473c05b59cfc9f4d37022\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "606,607,608,634,635,636,711,4914", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "30479,30538,30586,32335,32410,32486,36988,247922", "endLines": "606,607,608,634,635,636,711,4933", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "30533,30581,30637,32405,32481,32553,37049,248712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1b4bec413f45f01a07cb3ae9eb560838\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,63,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,124,130,131,132,133,136,139,142,143,146,149,150,151,152,153,156,159,160,161,162,168,173,176,179,180,181,186,187,188,191,194,195,198,201,204,207,208,209,212,215,216,221,222,228,233,236,239,240,241,242,243,244,245,246,247,248,249,250,266,272,273,274,275,276,283,289,290,291,294,299,300,308,309,310,311,312,313,314,315,324,325,326,332,333,339,343,344,345,346,347,356,360,361,362,380,566,694,700,704,874,1026,1039,1055,1080,1103,1106,1109,1112,1141,1168,1185,1271,1279,1292,1308,1312,1342,1355,1359,1369,1379,1423,1436,1440,1443,1459,1500,1535,1542,1559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,339,395,581,642,933,985,1035,1088,1136,1187,1242,1302,1367,1426,1488,1540,1601,1663,1709,1842,1894,1944,1995,2402,2714,2759,2818,3015,3072,3267,3448,3502,3559,3751,3809,4005,4061,4255,4312,4363,4585,4637,4692,4882,5098,5148,5200,5256,5462,5523,5583,5653,5786,5917,6045,6113,6242,6368,6430,6493,6561,6628,6751,6876,6943,7008,7073,7362,7543,7664,7785,7851,7918,8128,8197,8263,8388,8514,8581,8707,8834,8959,9086,9142,9207,9333,9456,9521,9729,9796,10084,10264,10384,10504,10569,10631,10693,10757,10819,10878,10938,10999,11060,11119,11179,11870,12121,12172,12221,12269,12327,12619,12849,12896,12956,13062,13242,13296,13631,13685,13741,13787,13834,13885,13944,13996,14326,14385,14439,14677,14732,15022,15161,15207,15262,15307,15351,15699,15836,15877,15922,16859,25449,31222,31597,31764,39466,46265,46962,47713,48588,49458,49524,49603,49678,51026,52013,52976,56913,57318,57789,58580,58743,60104,60668,60821,61280,61698,63711,64248,64398,64518,65165,66854,68275,68628,69370", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,63,64,69,70,75,80,81,82,87,88,93,94,99,100,101,107,108,109,114,120,121,122,123,129,130,131,132,135,138,141,142,145,148,149,150,151,152,155,158,159,160,161,167,172,175,178,179,180,185,186,187,190,193,194,197,200,203,206,207,208,211,214,215,220,221,227,232,235,238,239,240,241,242,243,244,245,246,247,248,249,265,271,272,273,274,275,282,288,289,290,293,298,299,307,308,309,310,311,312,313,314,323,324,325,331,332,338,342,343,344,345,346,355,359,360,361,379,565,693,699,703,873,1025,1038,1054,1079,1102,1105,1108,1111,1140,1167,1184,1270,1278,1291,1307,1311,1341,1354,1358,1368,1378,1422,1435,1439,1442,1458,1499,1534,1541,1558,1561", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "334,390,576,637,928,980,1030,1083,1131,1182,1237,1297,1362,1421,1483,1535,1596,1658,1704,1837,1889,1939,1990,2397,2709,2754,2813,3010,3067,3262,3443,3497,3554,3746,3804,4000,4056,4250,4307,4358,4580,4632,4687,4877,5093,5143,5195,5251,5457,5518,5578,5648,5781,5912,6040,6108,6237,6363,6425,6488,6556,6623,6746,6871,6938,7003,7068,7357,7538,7659,7780,7846,7913,8123,8192,8258,8383,8509,8576,8702,8829,8954,9081,9137,9202,9328,9451,9516,9724,9791,10079,10259,10379,10499,10564,10626,10688,10752,10814,10873,10933,10994,11055,11114,11174,11865,12116,12167,12216,12264,12322,12614,12844,12891,12951,13057,13237,13291,13626,13680,13736,13782,13829,13880,13939,13991,14321,14380,14434,14672,14727,15017,15156,15202,15257,15302,15346,15694,15831,15872,15917,16854,25444,31217,31592,31759,39461,46260,46957,47708,48583,49453,49519,49598,49673,51021,52008,52971,56908,57313,57784,58575,58738,60099,60663,60816,61275,61693,63706,64243,64393,64513,65160,66849,68270,68623,69365,69466"}, "to": {"startLines": "2,9,11,16,17,25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,44,45,46,47,57,66,89,90,95,96,101,106,107,108,113,114,119,120,125,126,127,133,134,135,140,146,147,150,151,157,158,159,160,163,166,169,170,173,176,177,178,179,180,183,186,187,188,189,195,200,203,206,207,208,213,214,215,218,221,222,225,228,231,234,235,236,239,242,243,248,249,255,260,263,266,267,268,269,270,271,272,273,274,275,276,277,293,299,300,301,302,304,311,317,318,319,322,327,328,336,337,338,368,369,370,372,373,382,383,384,390,391,397,401,402,403,404,405,414,714,750,3221,3288,3453,3581,3587,3591,3740,3975,4132,4148,4173,4196,4199,4202,4205,4232,4259,4276,4586,4594,4607,4623,4627,4657,4670,4674,4692,4702,4896,5052,5076,5202,5273,5310,5345,5397,5414", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,389,504,690,751,1042,1094,1144,1197,1245,1296,1351,1411,1476,1535,1597,1649,1710,1772,1879,2012,2064,2114,2165,2572,2939,3595,3654,3851,3908,4103,4284,4338,4395,4587,4645,4841,4897,5091,5148,5199,5421,5473,5528,5718,5934,5984,6130,6186,6392,6453,6513,6583,6716,6847,6975,7043,7172,7298,7360,7423,7491,7558,7681,7806,7873,7938,8003,8292,8473,8594,8715,8781,8848,9058,9127,9193,9318,9444,9511,9637,9764,9889,10016,10072,10137,10263,10386,10451,10659,10726,11014,11194,11314,11434,11499,11561,11623,11687,11749,11808,11868,11929,11990,12049,12109,12769,13020,13071,13120,13168,13286,13578,13808,13855,13915,14021,14201,14255,14590,14644,14700,15684,15731,15782,15882,15934,16264,16323,16377,16615,16670,16872,17011,17057,17112,17157,17201,17549,37124,38983,182148,185144,191262,196927,197302,197469,202699,213552,219279,220030,220884,221754,221820,221899,221974,222758,223649,224468,235528,235933,236404,237195,237358,238719,239283,239436,240143,240561,247411,252531,253219,258330,261108,261851,263272,265479,266221", "endLines": "8,9,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,43,44,45,46,56,64,66,89,94,95,100,105,106,107,112,113,118,119,124,125,126,132,133,134,139,145,146,147,150,156,157,158,159,162,165,168,169,172,175,176,177,178,179,182,185,186,187,188,194,199,202,205,206,207,212,213,214,217,220,221,224,227,230,233,234,235,238,241,242,247,248,254,259,262,265,266,267,268,269,270,271,272,273,274,275,276,292,298,299,300,301,302,310,316,317,318,321,326,327,335,336,337,338,368,369,370,372,381,382,383,389,390,396,400,401,402,403,404,413,417,714,750,3238,3452,3580,3586,3590,3739,3884,3987,4147,4172,4195,4198,4201,4204,4231,4258,4275,4361,4593,4606,4622,4626,4656,4669,4673,4683,4701,4745,4907,5055,5078,5217,5309,5344,5351,5413,5416", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "384,440,685,746,1037,1089,1139,1192,1240,1291,1346,1406,1471,1530,1592,1644,1705,1767,1813,2007,2059,2109,2160,2567,2879,2979,3649,3846,3903,4098,4279,4333,4390,4582,4640,4836,4892,5086,5143,5194,5416,5468,5523,5713,5929,5979,6031,6181,6387,6448,6508,6578,6711,6842,6970,7038,7167,7293,7355,7418,7486,7553,7676,7801,7868,7933,7998,8287,8468,8589,8710,8776,8843,9053,9122,9188,9313,9439,9506,9632,9759,9884,10011,10067,10132,10258,10381,10446,10654,10721,11009,11189,11309,11429,11494,11556,11618,11682,11744,11803,11863,11924,11985,12044,12104,12764,13015,13066,13115,13163,13221,13573,13803,13850,13910,14016,14196,14250,14585,14639,14695,14741,15726,15777,15836,15929,16259,16318,16372,16610,16665,16867,17006,17052,17107,17152,17196,17544,17681,37160,39023,183080,191257,196922,197297,197464,202694,208820,214244,220025,220879,221749,221815,221894,221969,222753,223644,224463,228400,235928,236399,237190,237353,238714,239278,239431,239890,240556,242569,247702,252676,253334,258972,261846,263267,263620,266216,266317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dad6ecdc4610e29e4f98bd0a0d7ae39e\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "445,446,447,448,595,596,819,821,822,823", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19701,19759,19825,19888,29675,29746,44682,44807,44874,44953", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "19754,19820,19883,19945,29741,29813,44745,44869,44948,45017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f40fc1ee1789a68053845db0e6462952\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "723,752", "startColumns": "4,4", "startOffsets": "37571,39071", "endColumns": "41,59", "endOffsets": "37608,39126"}}, {"source": "C:\\Users\\<USER>\\Desktop\\AMEUR APP\\mycar_maintenance\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "44", "endOffsets": "95"}, "to": {"startLines": "792", "startColumns": "4", "startOffsets": "41755", "endColumns": "44", "endOffsets": "41795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a29948c2f253e1876593f81e72801329\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "708,709,717,724,725,744,745,746,747,748", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "36867,36907,37275,37613,37668,38685,38739,38791,38840,38901", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "36902,36949,37313,37663,37710,38734,38786,38835,38896,38946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\10dcbe1ac2635d350953adab3ef6eeee\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "753", "startColumns": "4", "startOffsets": "39131", "endColumns": "53", "endOffsets": "39180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5d946368789e667402ea53bd4b56a62d\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "791", "startColumns": "4", "startOffsets": "41672", "endColumns": "82", "endOffsets": "41750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3afbe6dd2c3a497e6596084102e039dc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "421,496,652,653,654,655,656,657,658,718,719,720,761,762,818,820,833,856,868,869,870,1943,2127,2130,2136,2142,2145,2151,2155,2158,2165,2171,2174,2180,2185,2190,2197,2199,2205,2211,2219,2224,2231,2236,2242,2246,2253,2257,2263,2269,2272,2276,2277,3206,3239,3988,4026,4398,4684,4756,4820,4830,4840,4847,4853,4969,5145,5162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17862,22888,33536,33600,33655,33723,33790,33855,33912,37318,37366,37414,39610,39673,44644,44750,45821,48094,48714,48853,48903,117095,130833,130938,131183,131521,131667,132007,132219,132382,132789,133127,133250,133589,133828,134085,134456,134516,134854,135140,135589,135881,136269,136574,136918,137163,137493,137700,137968,138241,138385,138586,138633,181625,183085,214249,215550,229618,239895,242954,244879,245161,245466,245728,245988,249800,256365,256895", "endLines": "421,496,652,653,654,655,656,657,658,718,719,720,761,762,818,820,833,858,868,869,870,1959,2129,2135,2141,2144,2150,2154,2157,2164,2170,2173,2179,2184,2189,2196,2198,2204,2210,2218,2223,2230,2235,2241,2245,2252,2256,2262,2268,2271,2275,2276,2277,3210,3249,4007,4029,4407,4691,4819,4829,4839,4846,4852,4895,4981,5161,5178", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "17930,22952,33595,33650,33718,33785,33850,33907,33964,37361,37409,37470,39668,39731,44677,44802,45860,48229,48848,48898,48946,118528,130933,131178,131516,131662,132002,132214,132377,132784,133122,133245,133584,133823,134080,134451,134511,134849,135135,135584,135876,136264,136569,136913,137158,137488,137695,137963,138236,138380,138581,138628,138684,181805,183481,214973,215694,229945,240138,244874,245156,245461,245723,245983,247406,250247,256890,257458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\292a679d649b8a58fe89418be2f6dc59\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "10,67,73,81,339,351,357,363,364,365,366,367,706,2660,2666,5013,5021,5036", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "445,2984,3157,3376,14746,15060,15248,15435,15488,15548,15600,15645,36750,163916,164111,250981,251263,251877", "endLines": "10,72,80,88,350,356,362,363,364,365,366,367,706,2665,2670,5020,5035,5051", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "499,3152,3371,3590,15055,15243,15430,15483,15543,15595,15640,15679,36805,164106,164264,251258,251872,252526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fde86f55e71b0dcf886c49030e3a1cf4\\transformed\\jetified-appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2688,2704,2710,5056,5072", "startColumns": "4,4,4,4,4", "startOffsets": "164966,165391,165569,252681,253092", "endLines": "2703,2709,2719,5071,5075", "endColumns": "24,24,24,24,24", "endOffsets": "165386,165564,165848,253087,253214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c700a73c2e433e10f13879e3255c75cd\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "65,148,149,371,418,419,420,426,427,428,429,430,431,432,435,436,437,438,439,440,441,442,443,444,449,450,461,462,463,464,465,466,467,468,479,480,482,483,484,485,486,487,488,489,490,491,492,493,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,604,605,627,628,629,630,631,632,633,659,660,661,662,663,664,665,666,702,703,704,705,710,721,722,727,749,756,757,758,759,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,866,874,875,876,877,878,879,887,888,892,896,900,905,911,918,922,926,931,935,939,943,947,951,955,961,965,971,975,981,985,990,994,997,1001,1007,1011,1017,1021,1027,1030,1034,1038,1042,1046,1050,1051,1052,1053,1056,1059,1062,1065,1069,1070,1071,1072,1073,1076,1078,1080,1082,1087,1088,1092,1098,1102,1103,1105,1116,1117,1121,1127,1131,1132,1133,1137,1164,1168,1169,1173,1201,1370,1396,1565,1591,1622,1630,1636,1650,1672,1677,1682,1692,1701,1710,1714,1721,1729,1736,1737,1746,1749,1752,1756,1760,1764,1767,1768,1773,1778,1788,1793,1800,1806,1807,1810,1814,1819,1821,1823,1826,1829,1831,1835,1838,1845,1848,1851,1855,1857,1861,1863,1865,1867,1871,1879,1887,1899,1905,1914,1917,1928,1931,1932,1937,1938,1967,2036,2106,2107,2117,2126,2278,2280,2284,2287,2290,2293,2296,2299,2302,2305,2309,2312,2315,2318,2322,2325,2329,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2355,2357,2358,2359,2360,2361,2362,2363,2364,2366,2367,2369,2370,2372,2374,2375,2377,2378,2379,2380,2381,2382,2384,2385,2386,2387,2388,2400,2402,2404,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2420,2421,2422,2423,2424,2425,2426,2428,2432,2444,2445,2446,2447,2448,2449,2453,2454,2455,2456,2458,2460,2462,2464,2466,2467,2468,2469,2471,2473,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2489,2490,2491,2492,2494,2496,2497,2499,2500,2502,2504,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2519,2520,2521,2522,2524,2525,2526,2527,2528,2530,2532,2534,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2556,2631,2634,2637,2640,2654,2678,2720,2749,2776,2785,2847,3211,3260,4008,4362,4386,4392,4421,4442,4566,4746,4752,4908,4934,5001,5079,5182,5218,5352,5364,5390", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2884,6036,6081,15841,17686,17741,17800,18195,18259,18329,18390,18465,18541,18618,18856,18941,19023,19099,19175,19252,19330,19436,19542,19621,19950,20007,20867,20941,21016,21081,21147,21207,21268,21340,21892,21959,22084,22143,22202,22261,22320,22379,22433,22487,22540,22594,22648,22702,22957,23031,23110,23183,23257,23328,23400,23472,23545,23602,23660,23733,23807,23881,23956,24028,24101,24171,24242,24302,24363,24432,24501,24571,24645,24721,24785,24862,24938,25015,25080,25149,25226,25301,25370,25438,25515,25581,25642,25739,25804,25873,25972,26043,26102,26160,26217,26276,26340,26411,26483,26555,26627,26699,26766,26834,26902,26961,27024,27088,27178,27269,27329,27395,27462,27528,27598,27662,27715,27782,27843,27910,28023,28081,28144,28209,28274,28349,28422,28494,28543,28604,28665,28726,28788,28852,28916,28980,29045,29108,29168,29229,29295,29354,29414,29476,29547,29607,30306,30392,31724,31814,31901,31989,32071,32154,32244,33969,34021,34079,34124,34190,34254,34311,34368,36545,36602,36650,36699,36954,37475,37522,37780,38951,39299,39363,39425,39485,39806,39880,39950,40028,40082,40152,40237,40285,40331,40392,40455,40521,40585,40656,40719,40784,40848,40909,40970,41022,41095,41169,41238,41313,41387,41461,41602,48590,49120,49198,49288,49376,49472,49562,50144,50233,50480,50761,51013,51298,51691,52168,52390,52612,52888,53115,53345,53575,53805,54035,54262,54681,54907,55332,55562,55990,56209,56492,56700,56831,57058,57484,57709,58136,58357,58782,58902,59178,59479,59803,60094,60408,60545,60676,60781,61023,61190,61394,61602,61873,61985,62097,62202,62319,62533,62679,62819,62905,63253,63341,63587,64005,64254,64336,64434,65026,65126,65378,65802,66057,66151,66240,66477,68501,68743,68845,69098,71254,81695,83211,93750,95278,97035,97661,98081,99142,100407,100663,100899,101446,101940,102545,102743,103323,103887,104262,104380,104918,105075,105271,105544,105800,105970,106111,106175,106540,106907,107583,107847,108185,108538,108632,108818,109124,109386,109511,109638,109877,110088,110207,110400,110577,111032,111213,111335,111594,111707,111894,111996,112103,112232,112507,113015,113511,114388,114682,115252,115401,116133,116305,116389,116725,116817,118883,124129,129518,129580,130158,130742,138689,138802,139031,139191,139343,139514,139680,139849,140016,140179,140422,140592,140765,140936,141210,141409,141614,141944,142028,142124,142220,142318,142418,142520,142622,142724,142826,142928,143028,143124,143236,143365,143488,143619,143750,143848,143962,144056,144196,144330,144426,144538,144638,144754,144850,144962,145062,145202,145338,145502,145632,145790,145940,146081,146225,146360,146472,146622,146750,146878,147014,147146,147276,147406,147518,148416,148562,148706,148844,148910,149000,149076,149180,149270,149372,149480,149588,149688,149768,149860,149958,150068,150120,150198,150304,150396,150500,150610,150732,150895,151450,151530,151630,151720,151830,151920,152161,152255,152361,152453,152553,152665,152779,152895,153011,153105,153219,153331,153433,153553,153675,153757,153861,153981,154107,154205,154299,154387,154499,154615,154737,154849,155024,155140,155226,155318,155430,155554,155621,155747,155815,155943,156087,156215,156284,156379,156494,156607,156706,156815,156926,157037,157138,157243,157343,157473,157564,157687,157781,157893,157979,158083,158179,158267,158385,158489,158593,158719,158807,158915,159015,159105,159215,159299,159401,159485,159539,159603,159709,159795,159905,159989,160393,163009,163127,163242,163322,163683,164449,165853,167197,168558,168946,171721,181810,183787,214978,228405,229156,229418,230265,230644,234922,242574,242803,247707,248717,250669,253339,257586,258977,263625,263965,265276", "endLines": "65,148,149,371,418,419,420,426,427,428,429,430,431,432,435,436,437,438,439,440,441,442,443,444,449,450,461,462,463,464,465,466,467,468,479,480,482,483,484,485,486,487,488,489,490,491,492,493,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,604,605,627,628,629,630,631,632,633,659,660,661,662,663,664,665,666,702,703,704,705,710,721,722,727,749,756,757,758,759,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,866,874,875,876,877,878,886,887,891,895,899,904,910,917,921,925,930,934,938,942,946,950,954,960,964,970,974,980,984,989,993,996,1000,1006,1010,1016,1020,1026,1029,1033,1037,1041,1045,1049,1050,1051,1052,1055,1058,1061,1064,1068,1069,1070,1071,1072,1075,1077,1079,1081,1086,1087,1091,1097,1101,1102,1104,1115,1116,1120,1126,1130,1131,1132,1136,1163,1167,1168,1172,1200,1369,1395,1564,1590,1621,1629,1635,1649,1671,1676,1681,1691,1700,1709,1713,1720,1728,1735,1736,1745,1748,1751,1755,1759,1763,1766,1767,1772,1777,1787,1792,1799,1805,1806,1809,1813,1818,1820,1822,1825,1828,1830,1834,1837,1844,1847,1850,1854,1856,1860,1862,1864,1866,1870,1878,1886,1898,1904,1913,1916,1927,1930,1931,1936,1937,1942,2035,2105,2106,2116,2125,2126,2279,2283,2286,2289,2292,2295,2298,2301,2304,2308,2311,2314,2317,2321,2324,2328,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2354,2356,2357,2358,2359,2360,2361,2362,2363,2365,2366,2368,2369,2371,2373,2374,2376,2377,2378,2379,2380,2381,2383,2384,2385,2386,2387,2388,2401,2403,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2419,2420,2421,2422,2423,2424,2425,2427,2431,2435,2444,2445,2446,2447,2448,2452,2453,2454,2455,2457,2459,2461,2463,2465,2466,2467,2468,2470,2472,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2488,2489,2490,2491,2493,2495,2496,2498,2499,2501,2503,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2518,2519,2520,2521,2523,2524,2525,2526,2527,2529,2531,2533,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2630,2633,2636,2639,2653,2659,2687,2748,2775,2784,2846,3205,3214,3287,4025,4385,4391,4397,4441,4565,4585,4751,4755,4913,4968,5012,5144,5201,5272,5363,5389,5396", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2934,6076,6125,15877,17736,17795,17857,18254,18324,18385,18460,18536,18613,18691,18936,19018,19094,19170,19247,19325,19431,19537,19616,19696,20002,20060,20936,21011,21076,21142,21202,21263,21335,21408,21954,22022,22138,22197,22256,22315,22374,22428,22482,22535,22589,22643,22697,22751,23026,23105,23178,23252,23323,23395,23467,23540,23597,23655,23728,23802,23876,23951,24023,24096,24166,24237,24297,24358,24427,24496,24566,24640,24716,24780,24857,24933,25010,25075,25144,25221,25296,25365,25433,25510,25576,25637,25734,25799,25868,25967,26038,26097,26155,26212,26271,26335,26406,26478,26550,26622,26694,26761,26829,26897,26956,27019,27083,27173,27264,27324,27390,27457,27523,27593,27657,27710,27777,27838,27905,28018,28076,28139,28204,28269,28344,28417,28489,28538,28599,28660,28721,28783,28847,28911,28975,29040,29103,29163,29224,29290,29349,29409,29471,29542,29602,29670,30387,30474,31809,31896,31984,32066,32149,32239,32330,34016,34074,34119,34185,34249,34306,34363,34417,36597,36645,36694,36745,36983,37517,37566,37821,38978,39358,39420,39480,39537,39875,39945,40023,40077,40147,40232,40280,40326,40387,40450,40516,40580,40651,40714,40779,40843,40904,40965,41017,41090,41164,41233,41308,41382,41456,41597,41667,48638,49193,49283,49371,49467,49557,50139,50228,50475,50756,51008,51293,51686,52163,52385,52607,52883,53110,53340,53570,53800,54030,54257,54676,54902,55327,55557,55985,56204,56487,56695,56826,57053,57479,57704,58131,58352,58777,58897,59173,59474,59798,60089,60403,60540,60671,60776,61018,61185,61389,61597,61868,61980,62092,62197,62314,62528,62674,62814,62900,63248,63336,63582,64000,64249,64331,64429,65021,65121,65373,65797,66052,66146,66235,66472,68496,68738,68840,69093,71249,81690,83206,93745,95273,97030,97656,98076,99137,100402,100658,100894,101441,101935,102540,102738,103318,103882,104257,104375,104913,105070,105266,105539,105795,105965,106106,106170,106535,106902,107578,107842,108180,108533,108627,108813,109119,109381,109506,109633,109872,110083,110202,110395,110572,111027,111208,111330,111589,111702,111889,111991,112098,112227,112502,113010,113506,114383,114677,115247,115396,116128,116300,116384,116720,116812,117090,124124,129513,129575,130153,130737,130828,138797,139026,139186,139338,139509,139675,139844,140011,140174,140417,140587,140760,140931,141205,141404,141609,141939,142023,142119,142215,142313,142413,142515,142617,142719,142821,142923,143023,143119,143231,143360,143483,143614,143745,143843,143957,144051,144191,144325,144421,144533,144633,144749,144845,144957,145057,145197,145333,145497,145627,145785,145935,146076,146220,146355,146467,146617,146745,146873,147009,147141,147271,147401,147513,147653,148557,148701,148839,148905,148995,149071,149175,149265,149367,149475,149583,149683,149763,149855,149953,150063,150115,150193,150299,150391,150495,150605,150727,150890,151047,151525,151625,151715,151825,151915,152156,152250,152356,152448,152548,152660,152774,152890,153006,153100,153214,153326,153428,153548,153670,153752,153856,153976,154102,154200,154294,154382,154494,154610,154732,154844,155019,155135,155221,155313,155425,155549,155616,155742,155810,155938,156082,156210,156279,156374,156489,156602,156701,156810,156921,157032,157133,157238,157338,157468,157559,157682,157776,157888,157974,158078,158174,158262,158380,158484,158588,158714,158802,158910,159010,159100,159210,159294,159396,159480,159534,159598,159704,159790,159900,159984,160104,163004,163122,163237,163317,163678,163911,164961,167192,168553,168941,171716,181620,181940,185139,215545,229151,229413,229613,230639,234917,235523,242798,242949,247917,249795,250976,256360,258325,261103,263960,265271,265474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\326f41f20e705ccb630bd6dafc323fc5\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "751", "startColumns": "4", "startOffsets": "39028", "endColumns": "42", "endOffsets": "39066"}}, {"source": "C:\\Users\\<USER>\\Desktop\\AMEUR APP\\mycar_maintenance\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1960,1964", "startColumns": "4,4", "startOffsets": "118533,118714", "endLines": "1963,1966", "endColumns": "12,12", "endOffsets": "118709,118878"}}]}]}