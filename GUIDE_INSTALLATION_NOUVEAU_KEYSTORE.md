# 📱 Guide d'Installation - Nouveau Keystore

## 🎯 Problème
Votre téléphone a une version de l'app signée avec l'**ancien keystore** (`carosti-release-key.jks`), mais vous voulez installer la nouvelle version signée avec le **nouveau keystore** (`google-play-release.jks`).

Android refuse l'installation car les signatures sont différentes.

## ✅ Solutions

### **🔧 MÉTHODE 1 - Désinstallation manuelle (RECOMMANDÉE)**

1. **Sur votre téléphone :**
   - Allez dans `Paramètres` > `Applications` 
   - Trouvez `MyCar Maintenance` ou `Carosti`
   - Appuyez sur `Désinstaller`
   - Confirmez la désinstallation

2. **Transférer l'APK :**
   - Copiez le fichier `build\app\outputs\flutter-apk\app-debug.apk` sur votre téléphone
   - Via USB, Bluetooth, email, ou cloud

3. **Installer la nouvelle version :**
   - Ouvrez l'APK depuis le gestionnaire de fichiers
   - Activez `Sources inconnues` si demandé
   - Installez l'application

### **🔧 MÉTHODE 2 - Via ADB (Automatique)**

1. **Prérequis :**
   - Android Studio installé OU Platform Tools téléchargés
   - Débogage USB activé sur le téléphone
   - Téléphone connecté en USB

2. **Exécution :**
   ```bash
   # Double-cliquez sur le fichier :
   force_install_adb.bat
   ```

3. **Commandes manuelles (si nécessaire) :**
   ```bash
   adb devices
   adb uninstall com.carosti.app
   adb install build\app\outputs\flutter-apk\app-debug.apk
   ```

### **🔧 MÉTHODE 3 - Installation avec remplacement forcé**

Si vous avez des outils avancés :
```bash
adb install -r -d build\app\outputs\flutter-apk\app-debug.apk
```

## 📋 Fichiers Disponibles

### **APK Debug (Prêt) :**
- **Fichier :** `build\app\outputs\flutter-apk\app-debug.apk`
- **Signature :** Nouveau keystore
- **Taille :** ~50MB
- **Status :** ✅ Construit avec succès

### **APK Release (Problème) :**
- **Fichier :** `build\app\outputs\flutter-apk\app-release.apk`
- **Status :** ❌ Erreur de construction (problème Gradle/Java)
- **Solution :** Utiliser l'APK debug pour les tests

### **AAB Release (OK) :**
- **Fichier :** `build\app\outputs\bundle\release\app-release.aab`
- **Status :** ✅ Construit avec succès
- **Usage :** Pour upload sur Google Play Store

## ⚠️ Points Importants

### **Perte de Données :**
- ❌ **Toutes les données de l'app seront perdues** lors de la désinstallation
- 💾 **Sauvegardez vos données** avant la désinstallation si possible

### **Signatures Différentes :**
- **Ancien :** `carosti-release-key.jks` (alias: carosti)
- **Nouveau :** `google-play-release.jks` (alias: carosti)
- **Résultat :** Android les considère comme des apps différentes

### **Compatibilité :**
- ✅ **Google Play Store :** Nouveau keystore compatible
- ✅ **Installation directe :** Fonctionne après désinstallation
- ❌ **Mise à jour directe :** Impossible sans désinstallation

## 🚀 Scripts Disponibles

### **1. Guide d'installation :**
```bash
install_new_version.bat
```
- Affiche les instructions détaillées
- Ouvre le dossier contenant l'APK

### **2. Installation automatique ADB :**
```bash
force_install_adb.bat
```
- Détecte ADB automatiquement
- Désinstalle et réinstalle automatiquement

## 🔧 Résolution de Problèmes

### **"Sources inconnues" bloquées :**
1. Allez dans `Paramètres` > `Sécurité`
2. Activez `Sources inconnues` ou `Installer des apps inconnues`
3. Autorisez votre gestionnaire de fichiers

### **ADB non trouvé :**
1. Installez Android Studio
2. Ou téléchargez Platform Tools
3. Ajoutez au PATH système

### **Téléphone non détecté :**
1. Activez le `Mode développeur`
2. Activez le `Débogage USB`
3. Autorisez l'ordinateur sur le téléphone

## 🎉 Après Installation

Une fois installée, la nouvelle version :
- ✅ Sera signée avec le nouveau keystore
- ✅ Sera compatible avec Google Play Store
- ✅ Pourra recevoir des mises à jour depuis le Play Store
- ❌ Aura perdu toutes les données précédentes

## 📞 Support

Si vous rencontrez des problèmes :
1. Vérifiez que l'APK debug existe
2. Essayez la méthode manuelle en premier
3. Vérifiez les paramètres de sécurité Android
4. Consultez les logs ADB si nécessaire
