{"logs": [{"outputFile": "com.carosti.app-mergeDebugResources-50:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1ac570f9a9ce8a2badd3127fcdbc117\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "29,30,31,32,33,34,35,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2758,2851,2953,3048,3151,3254,3356,7964", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "2846,2948,3043,3146,3249,3351,3465,8060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\80b747fd0bf560516ae3aa3beeee4d5c\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4424", "endColumns": "129", "endOffsets": "4549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3afbe6dd2c3a497e6596084102e039dc\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "54,56,63,75,85,86,87", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5566,5733,6289,7390,8065,8234,8316", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "5628,5821,6370,7518,8229,8311,8391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1e115093ecfdcf00d62fda419f1b05b4\\transformed\\jetified-play-services-ads-23.6.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,406,474,573,634,755,871,1006,1059,1116,1228,1313,1351,1430,1462,1493,1536,1604,1644", "endColumns": "40,47,53,63,67,98,60,120,115,134,52,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,405,473,572,633,754,870,1005,1058,1115,1227,1312,1350,1429,1461,1492,1535,1603,1643,1699"}, "to": {"startLines": "60,61,62,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6134,6179,6231,6375,6443,6515,6618,6683,6808,6928,7067,7124,7185,7301,7523,7565,7648,7684,7719,7766,7838,8396", "endColumns": "44,51,57,67,71,102,64,124,119,138,56,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "6174,6226,6284,6438,6510,6613,6678,6803,6923,7062,7119,7180,7296,7385,7560,7643,7679,7714,7761,7833,7877,8451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f200f4a28092d3753dbbadd5663700bb\\transformed\\jetified-play-services-base-18.0.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3470,3574,3718,3840,3945,4083,4211,4322,4554,4691,4795,4945,5067,5206,5352,5416,5482", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "3569,3713,3835,3940,4078,4206,4317,4419,4686,4790,4940,5062,5201,5347,5411,5477,5561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dad6ecdc4610e29e4f98bd0a0d7ae39e\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5633,5826,5924,6032", "endColumns": "99,97,107,101", "endOffsets": "5728,5919,6027,6129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c700a73c2e433e10f13879e3255c75cd\\transformed\\appcompat-1.2.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,879,970,1063,1155,1249,1349,1442,1537,1630,1721,1815,1894,1999,2097,2195,2303,2403,2506,2661,2758", "endColumns": "107,103,106,81,100,113,79,77,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,874,965,1058,1150,1244,1344,1437,1532,1625,1716,1810,1889,1994,2092,2190,2298,2398,2501,2656,2753,2835"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,879,970,1063,1155,1249,1349,1442,1537,1630,1721,1815,1894,1999,2097,2195,2303,2403,2506,2661,7882", "endColumns": "107,103,106,81,100,113,79,77,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,874,965,1058,1150,1244,1344,1437,1532,1625,1716,1810,1889,1994,2092,2190,2298,2398,2501,2656,2753,7959"}}]}]}