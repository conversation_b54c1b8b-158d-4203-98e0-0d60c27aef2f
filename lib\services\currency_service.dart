import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service de gestion des devises
class CurrencyService extends ChangeNotifier {
  static final CurrencyService _instance = CurrencyService._internal();
  factory CurrencyService() => _instance;
  CurrencyService._internal();

  static const String _currencyKey = 'selected_currency';
  
  // Devises supportées
  static const Map<String, Map<String, dynamic>> _currencies = {
    'EUR': {
      'name': 'Euro',
      'symbol': '€',
      'code': 'EUR',
      'position': 'after', // Symbole après le montant
    },
    'USD': {
      'name': 'Dollar US',
      'symbol': '\$',
      'code': 'USD',
      'position': 'after', // Symbole après le montant (comme EUR et DZD)
    },
    'DZD': {
      'name': 'Dinar Algérien',
      'symbol': 'DA',
      'code': 'DZD',
      'position': 'after', // Symbole après le montant
    },
  };

  String _selectedCurrency = 'EUR'; // Par défaut Euro

  // Getters
  String get selectedCurrency => _selectedCurrency;
  String get currencySymbol => _currencies[_selectedCurrency]!['symbol'];
  String get currencyName => _currencies[_selectedCurrency]!['name'];
  String get currencyCode => _currencies[_selectedCurrency]!['code'];
  bool get isSymbolAfter => _currencies[_selectedCurrency]!['position'] == 'after';

  /// Initialise le service
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _selectedCurrency = prefs.getString(_currencyKey) ?? 'EUR';
    notifyListeners();
  }

  /// Change la devise sélectionnée
  Future<void> setCurrency(String currencyCode) async {
    if (_currencies.containsKey(currencyCode) && _selectedCurrency != currencyCode) {
      _selectedCurrency = currencyCode;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_currencyKey, currencyCode);
      
      notifyListeners();
    }
  }

  /// Formate un montant avec la devise actuelle
  String formatCost(double cost) {
    final symbol = currencySymbol;
    final formattedAmount = _formatAmount(cost);
    
    if (isSymbolAfter) {
      return '$formattedAmount $symbol';
    } else {
      return '$symbol$formattedAmount';
    }
  }

  /// Formate un montant compact (avec k pour milliers)
  String formatCostCompact(double cost) {
    final symbol = currencySymbol;
    String formattedAmount;
    
    if (cost >= 1000) {
      formattedAmount = '${(cost / 1000).toStringAsFixed(1)}k';
    } else {
      formattedAmount = cost.toStringAsFixed(0);
    }
    
    if (isSymbolAfter) {
      return '$formattedAmount $symbol';
    } else {
      return '$symbol$formattedAmount';
    }
  }

  /// Formate un montant avec précision décimale
  String formatCostPrecise(double cost) {
    final symbol = currencySymbol;
    final formattedAmount = cost.toStringAsFixed(2);
    
    if (isSymbolAfter) {
      return '$formattedAmount $symbol';
    } else {
      return '$symbol$formattedAmount';
    }
  }

  /// Formate un montant simple (entier)
  String _formatAmount(double cost) {
    return cost.toStringAsFixed(0);
  }

  /// Obtient toutes les devises disponibles
  List<Map<String, dynamic>> getAvailableCurrencies() {
    return _currencies.entries.map((entry) {
      return {
        'code': entry.key,
        'name': entry.value['name'],
        'symbol': entry.value['symbol'],
        'isSelected': entry.key == _selectedCurrency,
      };
    }).toList();
  }

  /// Obtient l'icône appropriée pour la devise
  IconData getCurrencyIcon() {
    switch (_selectedCurrency) {
      case 'EUR':
        return Icons.euro;
      case 'USD':
        return Icons.attach_money;
      case 'DZD':
        return Icons.currency_exchange; // Icône plus appropriée pour DA
      default:
        return Icons.monetization_on;
    }
  }

  /// Obtient le suffixe pour les champs de saisie
  String getCurrencySuffix() {
    return isSymbolAfter ? currencySymbol : '';
  }

  /// Obtient le préfixe pour les champs de saisie
  String getCurrencyPrefix() {
    return !isSymbolAfter ? currencySymbol : '';
  }

  /// Vérifie si une devise est supportée
  bool isCurrencySupported(String currencyCode) {
    return _currencies.containsKey(currencyCode);
  }

  /// Obtient les informations d'une devise
  Map<String, dynamic>? getCurrencyInfo(String currencyCode) {
    return _currencies[currencyCode];
  }
}
