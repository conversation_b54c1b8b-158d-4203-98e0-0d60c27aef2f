import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../services/theme_service.dart';
import '../services/currency_service.dart';
import '../services/localization_service.dart';
import '../services/import_export_service.dart';
import '../l10n/app_localizations.dart';
import 'vehicle_config_screen.dart';
import 'vehicle_management_screen.dart';
import 'maintenance_intervals_screen.dart';
import 'about_screen.dart';
// import 'data_management_screen.dart'; // Temporairement désactivé
import 'theme_settings_screen.dart';

import 'help_faq_screen.dart';
import 'notification_settings_screen.dart';
import 'localization_test_screen.dart';
import '../widgets/currency_selection_popup.dart';
import '../widgets/language_selector.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';
import '../widgets/shimmer_loading.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.settings,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Section Véhicule
              _buildSectionHeader(context, l10n.vehicle, MdiIcons.car),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: l10n.configVehicle,
                subtitle:
                    provider.currentVehicle?.vehicleName ?? l10n.noVehicle,
                icon: MdiIcons.carCog,
                onTap: () => _navigateToVehicleConfig(context),
              ),

              const SizedBox(height: 8),

              _buildSettingsTile(
                context,
                title: l10n.myVehicles,
                subtitle: '${provider.allVehicles.length} ${l10n.vehiclesConfigured}',
                icon: MdiIcons.carMultiple,
                onTap: () => _navigateToVehicleManagement(context),
              ),

              const SizedBox(height: 24),

              // Section Entretien
              _buildSectionHeader(context, l10n.maintenance, MdiIcons.wrench),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: l10n.intervals,
                subtitle: l10n.defaultAndCustomIntervals,
                icon: MdiIcons.clockOutline,
                onTap: () => _navigateToMaintenanceIntervals(context),
              ),

              _buildSettingsTile(
                context,
                title: l10n.notifications,
                subtitle: l10n.notificationReminders,
                icon: MdiIcons.bell,
                onTap: () => _navigateToNotificationSettings(context),
              ),

              const SizedBox(height: 24),

              // Section Données
              _buildSectionHeader(context, l10n.data, MdiIcons.database),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: l10n.importExport,
                subtitle: l10n.backupAndRestore,
                icon: Icons.import_export,
                onTap: () => _showImportExport(context),
              ),

              const SizedBox(height: 24),

              // Section Application
              _buildSectionHeader(context, l10n.application, MdiIcons.application),
              const SizedBox(height: 12),

              _buildSettingsTile(
                context,
                title: l10n.theme,
                subtitle: l10n.colorsAndAppearance,
                icon: MdiIcons.palette,
                onTap: () => _navigateToThemeSettings(context),
              ),

              Consumer<LocalizationService>(
                builder: (context, localizationService, child) {
                  return _buildSettingsTile(
                    context,
                    title: l10n.language,
                    subtitle: localizationService.getCurrentLanguageName(),
                    icon: Icons.language,
                    onTap: () => _showLanguageDialog(context),
                  );
                },
              ),

              _buildSettingsTile(
                context,
                title: l10n.currency,
                subtitle: 'Dollar, Euro, DZD',
                icon: Icons.monetization_on,
                onTap: () => CurrencySelectionPopup.show(context),
              ),

              _buildSettingsTile(
                context,
                title: l10n.about,
                subtitle: l10n.appInfo,
                icon: MdiIcons.informationOutline,
                onTap: () => _navigateToAbout(context),
              ),

              _buildSettingsTile(
                context,
                title: l10n.help,
                subtitle: l10n.guideAndFaq,
                icon: MdiIcons.helpCircleOutline,
                onTap: () => _navigateToHelpFaq(context),
              ),

              // Bouton de test pour la localisation (en développement)
              _buildSettingsTile(
                context,
                title: l10n.testLocalization,
                subtitle: l10n.testTranslations,
                icon: Icons.translate,
                onTap: () => _navigateToLocalizationTest(context),
                iconColor: Colors.orange,
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary, size: 18),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return ModernFadeIn(
      delay: const Duration(milliseconds: 100),
      child: ModernCard(
        enableHoverEffect: true,
        onTap: onTap,
        child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: (iconColor ?? Theme.of(context).colorScheme.primary)
                .withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor ?? Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontSize: 11,
          ),
        ),
        trailing: const Icon(Icons.chevron_right),
      ),
      ),
    );
  }

  void _navigateToVehicleConfig(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const VehicleConfigScreen()),
    );
  }

  void _navigateToVehicleManagement(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const VehicleManagementScreen()),
    );
  }

  void _navigateToMaintenanceIntervals(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MaintenanceIntervalsScreen(),
      ),
    );
  }

  // Import/Export
  void _showImportExport(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.import_export,
                  color: Colors.green,
                  size: 18,
                ),
              ),
              const SizedBox(width: 10),
              const Text(
                'Import/Export',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.save_alt, color: Colors.green, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${l10n.exportData}: ${l10n.createBackup} (.dat)',
                      style: const TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.folder_open, color: Colors.blue, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${l10n.importData}: ${l10n.restoreFromBackup}',
                      style: const TextStyle(fontSize: 13),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: Text(
                      l10n.cancel,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _handleImport(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.folder_open, size: 14),
                        const SizedBox(width: 4),
                        Text(l10n.import, style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _handleExport(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.save_alt, size: 14),
                        const SizedBox(width: 4),
                        Text(l10n.export, style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Gérer l'export
  Future<void> _handleExport(BuildContext context) async {
    final l10n = AppLocalizations.of(context);
    Navigator.of(context).pop(); // Fermer le dialog

    // Afficher un indicateur de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(20)),
        ),
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Export en cours...'),
          ],
        ),
      ),
    );

    try {
      // Fermer le dialog avant d'ouvrir la fenêtre de partage
      if (context.mounted) {
        Navigator.of(context).pop(); // Fermer l'indicateur de chargement
      }

      final success = await ImportExportService.exportData(context);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(success ? l10n.exportSuccess : l10n.exportError),
              ],
            ),
            backgroundColor: success ? Colors.green : Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(
          context,
        ).pop(); // Fermer l'indicateur de chargement en cas d'erreur

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text('Erreur: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  // Gérer l'import
  Future<void> _handleImport(BuildContext context) async {
    Navigator.of(context).pop(); // Fermer le dialog

    // Lancer l'import avec recréation de base et redirection
    debugPrint('🔍 Starting import...');
    try {
      final result = await ImportExportService.importData();
      debugPrint('🔍 Import result: ${result.success}');
      debugPrint('🔍 Import needsReload: ${result.needsReload}');
      debugPrint('🔍 Context mounted: ${context.mounted}');
      debugPrint('🔍 About to check context.mounted...');

      if (result.success) {
        debugPrint('🔍 Import réussi, rechargement des données...');

        // Les données sont importées, l'utilisateur peut naviguer pour les voir
        debugPrint('✅ Import terminé, les données sont dans la base');

        if (context.mounted) {
          // Afficher le message de succès
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ ${result.message}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );

          // Rediriger vers la page d'accueil si on n'y est pas déjà
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      } else {
        if (context.mounted) {
          // Afficher seulement les erreurs
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Erreur d\'import: ${result.message}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (error, stackTrace) {
      debugPrint('🔍 Import error: $error');
      debugPrint('🔍 Stack trace: $stackTrace');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _navigateToAbout(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AboutScreen()),
    );
  }

  void _navigateToThemeSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ThemeSettingsScreen()),
    );
  }

  void _navigateToHelpFaq(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HelpFaqScreen()),
    );
  }

  void _navigateToLocalizationTest(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LocalizationTestScreen()),
    );
  }

  void _navigateToNotificationSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NotificationSettingsScreen()),
    );
  }





  void _showNotImplementedDialog(BuildContext context, String feature) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature, style: const TextStyle(fontSize: 16)),
        content: const Text(
          'Fonctionnalité en développement.',
          style: TextStyle(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.ok),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final localizationService = context.read<LocalizationService>();
    final availableLanguages = localizationService.getAvailableLanguages();
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.language,
                  color: Theme.of(context).colorScheme.primary,
                  size: 18,
                ),
              ),
              const SizedBox(width: 10),
              Text(
                l10n.language,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: availableLanguages.map((language) {
              final isSelected = language['code'] == localizationService.languageCode;
              
              return ListTile(
                leading: _getLanguageFlag(language['code']!),
                title: Text(
                  language['nativeName']!,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
                trailing: isSelected 
                    ? Icon(Icons.check, color: Theme.of(context).colorScheme.primary) 
                    : null,
                onTap: () {
                  if (!isSelected) {
                    localizationService.changeLanguage(language['code']!);
                  }
                  Navigator.of(context).pop();
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancel),
            ),
          ],
        );
      },
    );
  }

  Widget _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return Container(
          width: 24,
          height: 18,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            border: Border.all(color: Colors.grey.shade300, width: 0.5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: Row(
              children: [
                Expanded(child: Container(color: const Color(0xFF0055A4))),
                Expanded(child: Container(color: Colors.white)),
                Expanded(child: Container(color: const Color(0xFFEF4135))),
              ],
            ),
          ),
        );
      case 'ar':
        return Container(
          width: 24,
          height: 18,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            border: Border.all(color: Colors.grey.shade300, width: 0.5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: Column(
              children: [
                Expanded(child: Container(color: const Color(0xFF006C35))),
                Expanded(child: Container(color: Colors.white)),
                Expanded(child: Container(color: Colors.black)),
                Expanded(child: Container(color: const Color(0xFFCE1126))),
              ],
            ),
          ),
        );
      default:
        return const Icon(Icons.language, size: 24);
    }
  }
}
