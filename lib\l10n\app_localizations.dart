import 'package:flutter/material.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_ar.dart';

abstract class AppLocalizations {
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    delegate,
  ];

  static const List<Locale> supportedLocales = [
    Locale('fr', 'FR'),
    Locale('ar', 'SA'),
  ];

  // Navigation et interface générale
  String get appTitle;
  String get home;
  String get settings;
  String get notifications;
  String get statistics;
  String get about;
  String get help;
  String get language;
  String get theme;
  String get currency;
  String get premium;
  String get backup;
  String get restore;
  String get export;
  String get import;
  String get cancel;
  String get save;
  String get delete;
  String get edit;
  String get add;
  String get confirm;
  String get yes;
  String get no;
  String get ok;
  String get close;
  String get back;
  String get next;
  String get previous;
  String get loading;
  String get error;
  String get success;
  String get warning;
  String get info;

  // Véhicule
  String get vehicle;
  String get vehicles;
  String get addVehicle;
  String get editVehicle;
  String get deleteVehicle;
  String get vehicleName;
  String get brand;
  String get model;
  String get year;
  String get currentKilometers;
  String get licensePlate;
  String get engineType;
  String get selectVehicle;
  String get noVehicleSelected;
  String get defaultVehicle;
  String get switchVehicle;
  String get vehicleInfo;
  String get updateKilometers;
  String get enterKilometers;
  String get kilometersUpdated;
  String get invalidKilometers;

  // Entretien
  String get maintenance;
  String get maintenanceItems;
  String get addMaintenance;
  String get editMaintenance;
  String get deleteMaintenance;
  String get maintenanceName;
  String get maintenanceDescription;
  String get category;
  String get interval;
  String get defaultInterval;
  String get customInterval;
  String get lastMaintenance;
  String get nextMaintenance;
  String get maintenanceDue;
  String get maintenanceApproaching;
  String get maintenanceUpToDate;
  String get performMaintenance;
  String get maintenanceCompleted;
  String get maintenanceHistory;
  String get maintenanceDate;
  String get maintenanceCost;
  String get maintenanceLocation;
  String get mechanicName;
  String get notes;
  String get kmUntilNext;
  String get usagePercentage;
  String get active;
  String get inactive;

  // Catégories d'entretien
  String get engine;
  String get brakes;
  String get tires;
  String get fluids;
  String get filters;
  String get electrical;
  String get bodywork;
  String get interior;
  String get safety;
  String get other;

  // Documents
  String get documents;
  String get addDocument;
  String get editDocument;
  String get deleteDocument;
  String get documentName;
  String get documentDescription;
  String get expirationDate;
  String get companyName;
  String get documentNumber;
  String get documentExpired;
  String get documentExpiring;
  String get documentValid;
  String get insurance;
  String get registration;
  String get technicalInspection;
  String get warranty;
  String get invoice;
  String get receipt;
  String get manual;
  String get certificate;

  // Statistiques
  String get totalMaintenanceItems;
  String get itemsDue;
  String get itemsApproaching;
  String get itemsUpToDate;
  String get overview;
  String get quickActions;
  String get urgent;
  String get upcoming;
  String get upToDate;
  String get toProcess;
  String get toPlan;
  String get inOrder;
  String get configureVehicle;
  String get configure;
  String get updateKm;
  String get update;
  String get diagnostics;
  String get analyze;
  String get privacyPolicy;
  String get importExport;
  String get updateApp;
  String get rateApp;
  String get quit;
  String get hello;
  String get version;
  String get newVehicle;
  String get vehicleConfiguration;
  String get information;
  String get nameRequired;
  String get brandRequired;
  String get modelRequired;
  String get yearRequired;
  String get invalidYear;
  String get technical;
  String get kilometersRequired;
  String get saving;
  String get deleteVehicleTitle;
  String get deleteVehicleMessage;
  String get history;
  String get all;
  String get searchInHistory;
  String get performed;
  String get editKilometers;
  String get deleteThisMaintenance;
  String get cost;
  String get location;
  String get mechanic;
  String get noHistoryFound;
  String get maintenanceWillAppearHere;
  String get deletedMaintenance;
  String get deleteMaintenanceTitle;
  String get deleteMaintenanceConfirmation;
  String get thisActionCannotBeUndone;
  String get soon;
  String get progression;
  String get currentKm;
  String get last;
  String get remaining;
  String get exceededBy;
  String get actions;
  String get perform;
  String get noHistory;
  String get totalCost;
  String get averageCost;
  String get lastUpdate;
  String get maintenanceOverview;
  String get costAnalysis;
  String get monthlyStats;
  String get yearlyStats;
  String get mostExpensive;
  String get mostFrequent;
  String get upcomingMaintenance;

  // Notifications
  String get notificationTitle;
  String get notificationBody;
  String get enableNotifications;
  String get notificationSettings;
  String get dailyReminder;
  String get weeklyReminder;
  String get monthlyReminder;
  String get maintenanceReminder;
  String get documentReminder;
  String get reminderTime;
  String get noNotifications;
  String get markAsRead;
  String get clearAll;

  // Erreurs et messages
  String get errorLoadingData;
  String get errorSavingData;
  String get errorDeletingData;
  String get errorInvalidInput;
  String get errorNetworkConnection;
  String get errorDatabaseConnection;
  String get errorPermissionDenied;
  String get errorFileNotFound;
  String get errorUnknown;
  String get dataLoadedSuccessfully;
  String get dataSavedSuccessfully;
  String get dataDeletedSuccessfully;
  String get operationCompleted;
  String get operationFailed;
  String get confirmDelete;
  String get confirmDeleteMessage;
  String get cannotUndoAction;

  // Premium
  String get premiumFeatures;
  String get upgradeToPremium;
  String get premiumBenefits;
  String get unlimitedVehicles;
  String get advancedStatistics;
  String get cloudBackup;
  String get prioritySupport;
  String get adFree;
  String get customThemes;
  String get exportData;
  String get premiumUser;
  String get freeUser;
  String get purchasePremium;
  String get restorePurchases;

  // Sauvegarde et restauration
  String get backupData;
  String get restoreData;
  String get exportToFile;
  String get importFromFile;
  String get selectFile;
  String get backupCreated;
  String get dataRestored;
  String get backupFailed;
  String get restoreFailed;
  String get invalidBackupFile;
  String get backupInProgress;
  String get restoreInProgress;

  // Mise à jour
  String get updateAvailable;
  String get updateNow;
  String get updateLater;
  String get newVersion;
  String get currentVersion;
  String get whatsNew;
  String get updateFeatures;
  String get downloadUpdate;
  String get installUpdate;

  // Diagnostic
  String get diagnostic;
  String get runDiagnostic;
  String get diagnosticResults;
  String get systemHealth;
  String get databaseIntegrity;
  String get performanceCheck;
  String get storageUsage;
  String get cacheSize;
  String get clearCache;
  String get optimizeDatabase;
  String get repairDatabase;

  // Unités et formats
  String get kilometers;
  String get km;
  String get days;
  String get months;
  String get years;
  String get date;
  String get time;
  String get percentage;
  String get total;
  String get average;
  String get minimum;
  String get maximum;

  // Actions communes
  String get search;
  String get filter;
  String get sort;
  String get sortBy;
  String get ascending;
  String get descending;
  String get refresh;
  String get reload;
  String get reset;
  String get clear;
  String get apply;
  String get retry;
  String get undo;
  String get redo;
  String get copy;
  String get paste;
  String get share;
  String get print;
  String get preview;
  String get fullScreen;
  String get minimize;
  String get maximize;

  // Messages contextuels
  String get noDataAvailable;
  String get noItemsFound;
  String get noResultsFound;
  String get emptyList;
  String get addFirstItem;
  String get getStarted;
  String get welcomeMessage;
  String get tutorialTitle;
  String get skipTutorial;
  String get nextStep;
  String get previousStep;
  String get finish;
  String get congratulations;
  String get allDone;

  // Textes manquants pour la traduction complète
  String get dashboard;
  String get noVehicleConfigured;
  String get current;
  String get remainingKilometers;
  String get wear;
  String get noMaintenanceNeeded;
  String get expired;
  String get valid;
  String get planned;
  String get daysRemaining;
  String get dateNotDefined;
  String get mileage;
  String get today;
  String get yesterday;
  String get daysAgo;

  // About Screen
  String get description;
  String get appDescription;
  String get features;
  String get mileageTracking;
  String get mileageTrackingDesc;
  String get maintenanceManagement;
  String get maintenanceManagementDesc;
  String get ocrScan;
  String get ocrScanDesc;
  String get notificationsFeature;
  String get notificationsFeatureDesc;
  String get completeHistory;
  String get completeHistoryDesc;
  String get support;
  String get supportDescription;
  String get contactEmail;
  String get copyEmail;
  String get emailCopied;
  String get visitWebsite;
  String get cannotOpenWebsite;
  String get errorOpening;

  // Camera Scan Screen
  String get enterMileage;
  String get scanMileage;
  String get autoScanUnavailable;
  String get autoScanUnavailableDesc;
  String get processing;
  String get manualEntry;
  String get enterValidMileage;

  // Help FAQ Screen
  String get helpFaq;
  String get maintenances;
  String get data;
  String get discoverApp;
  String get discoverAppDesc;
  String get viewPresentation;
  String get quickGuide;
  String get followMaintenances;
  String get consultStats;
  String get noQuestionFound;

  // FAQ Questions and Answers
  String get faqAddVehicleQ;
  String get faqAddVehicleA;
  String get faqPerformMaintenanceQ;
  String get faqPerformMaintenanceA;
  String get faqNotificationsQ;
  String get faqNotificationsA;
  String get faqCustomIntervalsQ;
  String get faqCustomIntervalsA;
  String get faqAddCustomMaintenanceQ;
  String get faqAddCustomMaintenanceA;
  String get faqBackupDataQ;
  String get faqBackupDataA;
  String get faqChangeCurrencyQ;
  String get faqChangeCurrencyA;
  String get faqWearPercentageQ;
  String get faqWearPercentageA;

  // Maintenance Detail Screen
  String get informations;
  String get reloadHistoryComment;

  // Engine categories
  String get braking;
  String get cooling;
  String get comfort;

  // Perform Maintenance Screen
  String get maintenanceInfoComment;
  String get details;
  String get maintenanceKmLabel;
  String get maintenanceKmHint;
  String get enterKmError;
  String get invalidKmError;
  String get garage;
  String get observations;
  String get maintenanceHelpText;
  String get maintenanceSaved;

  // Maintenance Intervals Screen
  String get intervals;
  String get extinguisher;
  String get extinguisherControl;
  String get security;
  String get configIntervals;
  String get restoreDefault;
  String get custom;
  String get defaultValue;
  String get modify;
  String get modifyInterval;
  String get enterValidInterval;
  String get intervalUpdated;
  String get intervalRestored;
  String get restoreDefaultValues;
  String get featureInDevelopment;
  String get on;
  String get off;
  String get activate;
  String get deactivate;

  // Maintenance Items Names
  String get oilChangeEngine;
  String get oilChangeEngineDesc;
  String get fuelFilter;
  String get fuelFilterDesc;
  String get airFilter;
  String get airFilterDesc;
  String get cabinFilter;
  String get cabinFilterDesc;
  String get timingBelt;
  String get timingBeltDesc;
  String get coolantFluid;
  String get coolantFluidDesc;
  String get brakeFluid;
  String get brakeFluidDesc;
  String get brakePads;
  String get brakePadsDesc;
  String get tiresItem;
  String get tiresItemDesc;

  // Document Items Names
  String get insuranceDoc;
  String get insuranceDocDesc;
  String get technicalControlDoc;
  String get technicalControlDocDesc;
  String get extinguisherDoc;
  String get extinguisherDocDesc;

  // Categories Names
  String get engineCategory;
  String get filtersCategory;
  String get brakingCategory;
  String get tiresCategory;
  String get coolingCategory;
  String get comfortCategory;
  String get insuranceCategory;
  String get controlCategory;
  String get securityCategory;

  // Home Screen
  String get appName;
  String get toSchedule;

  // Maintenance Diagnostic Screen
  String get recreateDatabase;
  String get noVehicle;
  String get noMaintenanceRecorded;
  String get modifyKm;
  String get kmUpdated;
  String get comparison;
  String get comparisonKm;
  String get difference;
  String get expiration;
  String get remains;
  String get company;
  String get selectDate;

  // Settings Screen
  String get configVehicle;
  String get myVehicles;
  String get vehiclesConfigured;
  String get defaultAndCustomIntervals;
  String get notificationReminders;
  String get application;
  String get colorsAndAppearance;
  String get testLocalization;
  String get testTranslations;
  String get appInfo;
  String get exportSuccess;
  String get exportError;
  String get closeLoadingIndicator;
  String get redirectToHome;
  String get availableLanguages;
  String get backupAndRestore;
  String get guideAndFaq;

  // Vehicle Config Screen
  String get brandHint;
  String get engineHint;
  String get kmHint;
  String get kmSuffix;
  String get vehicleModified;
  String get vehicleAdded;
  String get noCurrentVehicle;
  String get myVehicle;
  String get vehicleDeletedDefaultCreated;

  // Statistics Screen
  String get analysisPeriod;
  String get oneYear;
  String get everything;
  String get distribution;
  String get maintenanceCosts;
  String get thisMonth;
  String get thisYear;
  String get noMaintenanceCostRecorded;
  String get distributionByMaintenance;
  String get predictions;
  String get nextMaintenances;
  String get now;
  String get tomorrow;
  String get inDays;
  String get inWeeks;
  String get weeksAgo;
  String get monthsAgo;
  String get yearsAgo;
  String get maintenanceType;
  String get enterOperation;
  String get addToHistory;
  String get modifyMaintenance;
  String get confirmDeletion;

  // Perform Maintenance Screen
  String get maintenanceDetails;
  String get enterMileageValidation;
  String get invalidMileage;
  String get maintenanceRecorded;

  // Maintenance Detail Screen
  String get exceeded;

  // Additional Maintenance Diagnostic
  String get recreateConfirm;
  String get databaseRecreated;

  // Additional Statistics Screen
  String get operation;

  // Help FAQ Screen - Additional
  String get configureVehicleStep;
  String get updateKmStep;
  String get followMaintenanceStep;
  String get consultStatsStep;
  String get addNewVehicleQuestion;
  String get addNewVehicleAnswer;
  String get scheduleMaintenanceQuestion;
  String get scheduleMaintenanceAnswer;
  String get noNotificationsQuestion;
  String get noNotificationsAnswer;
  String get modifyIntervalsQuestion;
  String get modifyIntervalsAnswer;
  String get addCustomOperationQuestion;
  String get addCustomOperationAnswer;
  String get exportDataQuestion;
  String get exportDataAnswer;
  String get changeCurrencyQuestion;
  String get changeCurrencyAnswer;
  String get wearPercentageQuestion;
  String get wearPercentageAnswer;

  // Privacy Policy Screen
  String get privacyPolicyTitle;
  String get dataCollection;
  String get dataCollectionText;
  String get dataUsage;
  String get dataUsageText;
  String get dataSharing;
  String get dataSharingText;
  String get dataSecurity;
  String get dataSecurityText;
  String get userRights;
  String get userRightsText;
  String get contactUs;
  String get contactUsText;
  String get lastUpdated;
  String get viewFullPolicy;
  String get cannotOpenLink;
  String get errorOpeningLink;
  String get privacyImportant;
  String get privacyDescription;
  String get allRightsReserved;

  // Presentation Screen
  String get skip;
  String get presentationWelcomeTitle;
  String get presentationWelcomeDesc;
  String get presentationMaintenanceTitle;
  String get presentationMaintenanceDesc;
  String get presentationNotificationsTitle;
  String get presentationNotificationsDesc;
  String get presentationHistoryTitle;
  String get presentationHistoryDesc;
  String get presentationStatsTitle;
  String get presentationStatsDesc;
  String get presentationDiagnosticTitle;
  String get presentationDiagnosticDesc;
  String get presentationReadyTitle;
  String get presentationReadyDesc;

  // Import Export Messages
  String get fileSelected;
  String get exportInProgress;
  String get exportComplete;
  String get restoreComplete;
  String get invalidFileFormat;
  String get fileNotFound;
  String get permissionDenied;
  String get storageAccessRequired;
  String get chooseBackupFile;
  String get createBackup;
  String get restoreFromBackup;
  String get dataWillBeReplaced;
  String get confirmRestore;
  String get backupSavedTo;

  // App Update Messages
  String get newVersionAvailable;
  String get currentVersionText;
  String get latestVersionText;
  String get improvements;
  String get downloadingUpdate;
  String get updateDownloaded;
  String get updateFailed;
  String get appUpToDate;
  String get updateRequired;
  String get criticalUpdate;
  String get skipThisVersion;

  // App Rating Messages
  String get rateAppTitle;
  String get rateAppMessage;
  String get rateAppPositive;
  String get rateAppNegative;
  String get rateAppNeutral;
  String get enjoyingApp;
  String get wouldYouRate;
  String get feedbackTitle;
  String get feedbackMessage;
  String get sendFeedback;
  String get thankYouRating;
  String get rateOnStore;
  String get maybeLater;
  String get noThanks;
  String get howCanImprove;
  String get tellUsMore;
  String get submitFeedback;
  String get feedbackSent;
  String get openStore;

  // Additional Help FAQ
  String get parameters;

  // Import Export Additional
  String get importData;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['fr', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    switch (locale.languageCode) {
      case 'ar':
        return AppLocalizationsAr();
      case 'fr':
      default:
        return AppLocalizationsFr();
    }
  }

  @override
  bool shouldReload(LocalizationsDelegate<AppLocalizations> old) => false;
}