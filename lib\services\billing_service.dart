import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

/// Service de gestion des achats in-app via Google Play Billing
class BillingService {
  static final BillingService _instance = BillingService._internal();
  factory BillingService() => _instance;
  BillingService._internal();

  // Instance du plugin in-app purchase
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  
  // Stream pour écouter les achats
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  // Produits disponibles
  List<ProductDetails> _products = [];
  
  // État du service
  bool _isAvailable = false;
  bool _isInitialized = false;
  
  // ID des produits (à configurer dans Google Play Console)
  static const String premiumProductId = 'carosti_premium_upgrade';
  static const Set<String> _productIds = {premiumProductId};
  
  // Callbacks
  Function(bool success, String? error)? onPurchaseComplete;
  Function(String error)? onPurchaseError;

  /// Initialise le service de billing
  Future<bool> initialize() async {
    if (_isInitialized) return _isAvailable;
    
    try {
      debugPrint('🛒 Initialisation Google Play Billing...');
      
      // Vérifier la disponibilité du service
      _isAvailable = await _inAppPurchase.isAvailable();
      
      if (!_isAvailable) {
        debugPrint('❌ Google Play Billing non disponible');
        return false;
      }
      
      // Configuration spécifique Android (enablePendingPurchases n'est plus nécessaire)
      if (Platform.isAndroid) {
        debugPrint('🤖 Configuration Android pour Google Play Billing');
      }
      
      // Écouter les achats
      _subscription = _inAppPurchase.purchaseStream.listen(
        _handlePurchaseUpdates,
        onDone: () => debugPrint('🛒 Purchase stream fermé'),
        onError: (error) => debugPrint('❌ Erreur purchase stream: $error'),
      );
      
      // Charger les produits
      await _loadProducts();
      
      // Restaurer les achats précédents
      await _restorePurchases();
      
      _isInitialized = true;
      debugPrint('✅ Google Play Billing initialisé avec succès');
      return true;
      
    } catch (e) {
      debugPrint('❌ Erreur initialisation billing: $e');
      return false;
    }
  }

  /// Charge les produits disponibles depuis Google Play
  Future<void> _loadProducts() async {
    try {
      debugPrint('🛒 Chargement des produits...');
      
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);
      
      if (response.error != null) {
        debugPrint('❌ Erreur chargement produits: ${response.error}');
        return;
      }
      
      _products = response.productDetails;
      debugPrint('✅ ${_products.length} produit(s) chargé(s)');
      
      for (final product in _products) {
        debugPrint('📦 Produit: ${product.id} - ${product.price}');
      }
      
    } catch (e) {
      debugPrint('❌ Erreur chargement produits: $e');
    }
  }

  /// Restaure les achats précédents
  Future<void> _restorePurchases() async {
    try {
      debugPrint('🔄 Restauration des achats...');
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('❌ Erreur restauration achats: $e');
    }
  }

  /// Gère les mises à jour d'achats
  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debugPrint('🛒 Mise à jour achat: ${purchaseDetails.productID} - ${purchaseDetails.status}');
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          debugPrint('⏳ Achat en attente...');
          break;
          
        case PurchaseStatus.purchased:
          debugPrint('✅ Achat réussi !');
          _handleSuccessfulPurchase(purchaseDetails);
          break;
          
        case PurchaseStatus.error:
          debugPrint('❌ Erreur achat: ${purchaseDetails.error}');
          _handlePurchaseError(purchaseDetails);
          break;
          
        case PurchaseStatus.restored:
          debugPrint('🔄 Achat restauré');
          _handleSuccessfulPurchase(purchaseDetails);
          break;
          
        case PurchaseStatus.canceled:
          debugPrint('❌ Achat annulé');
          onPurchaseComplete?.call(false, 'Achat annulé par l\'utilisateur');
          break;
      }
      
      // Finaliser l'achat si nécessaire
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Gère un achat réussi
  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    if (purchaseDetails.productID == premiumProductId) {
      debugPrint('🎉 Premium acheté avec succès !');
      onPurchaseComplete?.call(true, null);
    }
  }

  /// Gère une erreur d'achat
  void _handlePurchaseError(PurchaseDetails purchaseDetails) {
    final String error = purchaseDetails.error?.message ?? 'Erreur inconnue';
    debugPrint('❌ Erreur achat: $error');
    onPurchaseError?.call(error);
    onPurchaseComplete?.call(false, error);
  }

  /// Lance l'achat du Premium
  Future<bool> purchasePremium() async {
    if (!_isAvailable || !_isInitialized) {
      debugPrint('❌ Service billing non disponible');
      return false;
    }



    // Trouver le produit Premium
    final ProductDetails? premiumProduct = _products
        .where((product) => product.id == premiumProductId)
        .firstOrNull;

    if (premiumProduct == null) {
      debugPrint('❌ Produit Premium non trouvé');
      debugPrint('💡 Conseil: Configurez le produit "$premiumProductId" dans Google Play Console');
      debugPrint('📋 Étapes requises:');
      debugPrint('   1. Aller dans Google Play Console');
      debugPrint('   2. Monétisation > Produits in-app');
      debugPrint('   3. Créer produit avec ID: $premiumProductId');
      debugPrint('   4. Définir prix et activer le produit');
      return false;
    }

    try {
      debugPrint('🛒 Lancement achat Premium...');

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: premiumProduct,
      );

      final bool success = await _inAppPurchase.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      debugPrint('🛒 Achat initié: $success');
      return success;

    } catch (e) {
      debugPrint('❌ Erreur lancement achat: $e');
      return false;
    }
  }

  /// Vérifie si l'utilisateur a acheté le Premium
  Future<bool> hasPurchasedPremium() async {
    if (!_isAvailable || !_isInitialized) return false;
    
    try {
      await _inAppPurchase.restorePurchases();
      // La vérification se fait via les callbacks _handlePurchaseUpdates
      return true; // Le résultat sera géré par les callbacks
    } catch (e) {
      debugPrint('❌ Erreur vérification achats: $e');
      return false;
    }
  }

  /// Obtient le prix du Premium
  String? getPremiumPrice() {
    final ProductDetails? premiumProduct = _products
        .where((product) => product.id == premiumProductId)
        .firstOrNull;
    
    return premiumProduct?.price;
  }

  /// Obtient les détails du produit Premium
  ProductDetails? getPremiumProduct() {
    return _products
        .where((product) => product.id == premiumProductId)
        .firstOrNull;
  }

  /// Nettoie les ressources
  void dispose() {
    _subscription.cancel();
    debugPrint('🛒 BillingService fermé');
  }

  // Getters
  bool get isAvailable => _isAvailable;
  bool get isInitialized => _isInitialized;
  List<ProductDetails> get products => _products;
}
