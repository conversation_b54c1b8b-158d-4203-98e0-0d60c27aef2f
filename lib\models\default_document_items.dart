import 'document_item.dart';

class DefaultDocumentItems {
  static List<DocumentItem> getDefaultItems() {
    print('📄 CRÉATION DES DOCUMENTS PAR DÉFAUT');
    final items = [
      // 1. Assurance
      DocumentItem(
        name: 'Assurance',
        description: 'Assurance automobile obligatoire',
        iconPath: 'assets/icons/insurance.png',
        category: 'Assurance',
        isActive: true,
      ),
      // 2. Contrôle Technique
      DocumentItem(
        name: 'Contrôle technique',
        description: 'Contrôle technique périodique',
        iconPath: 'assets/icons/technical_control.png',
        category: 'Contrôle',
        isActive: true,
      ),
      // 3. Extincteur
      DocumentItem(
        name: 'Extincteur',
        description: 'Contrôle de l\'extincteur',
        iconPath: 'assets/icons/fire_extinguisher.png',
        category: 'Sécurité',
        isActive: false, // Désactivé par défaut - l'utilisateur peut l'activer
      ),
    ];
    
    // Log des documents pour débogage
    for (final item in items) {
      print('📋 ${item.name}: ${item.category}');
    }
    
    return items;
  }

  static Map<String, List<String>> getDocumentCategories() {
    return {
      'Assurance': ['Assurance'],
      'Contrôle': ['Contrôle Technique'],
      'Sécurité': ['Extincteur'],
    };
  }
}
