import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../services/connectivity_service.dart';
import '../services/premium_service.dart';
import '../models/vehicle_config.dart';
import '../models/maintenance_item.dart';
import '../models/document_item.dart';
import '../models/default_maintenance_items.dart';
import '../widgets/smart_admob_banner.dart';

class GlobalStatusScreen extends StatefulWidget {
  const GlobalStatusScreen({super.key});

  @override
  State<GlobalStatusScreen> createState() => _GlobalStatusScreenState();
}

class _GlobalStatusScreenState extends State<GlobalStatusScreen> {
  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();
    // Initialiser la connectivité
    _initializeConnectivity();

    // Charger les données de tous les véhicules
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MaintenanceProvider>().loadAllVehiclesData();
    });
  }

  Future<void> _initializeConnectivity() async {
    if (!_connectivityService.isInitialized) {
      await _connectivityService.initialize();
    }

    if (mounted) {
      setState(() {
        _isConnected = _connectivityService.isConnected;
      });
    }

    // Écouter les changements de connectivité
    _connectivityService.connectivityStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'État Global',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final vehicles = provider.allVehicles;
          if (vehicles.isEmpty) {
            return const Center(
              child: Text('Aucun véhicule configuré'),
            );
          }

          return Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: vehicles.length,
                  itemBuilder: (context, index) {
                    final vehicle = vehicles[index];
                    return _buildVehicleStatusCard(context, provider, vehicle);
                  },
                ),
              ),

              // Bannière publicitaire en bas (masquée si pas de connexion ou si Premium)
              StreamBuilder<bool>(
                stream: PremiumService().premiumStatusStream,
                initialData: PremiumService().isPremium,
                builder: (context, snapshot) {
                  final isPremium = snapshot.data ?? false;

                  if (isPremium || !_isConnected) {
                    return const SizedBox.shrink();
                  }

                  return const SmartAdMobBanner(pageId: 'global_status');
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildVehicleStatusCard(
    BuildContext context,
    MaintenanceProvider provider,
    VehicleConfig vehicle,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête du véhicule
            Row(
              children: [
                Icon(
                  MdiIcons.car,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        vehicle.vehicleName,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${vehicle.currentKilometers} km',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Entretiens actifs
            FutureBuilder<List<MaintenanceItem>>(
              future: provider.getMaintenanceItemsForVehicle(vehicle.id!),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const SizedBox(height: 20, child: LinearProgressIndicator());
                }
                
                final maintenanceItems = snapshot.data!
                    .where((item) =>
                        item.isActive &&
                        item.category.toLowerCase() != 'autre' &&
                        !item.name.startsWith('[CUSTOM]'))
                    .toList();

                // Trier selon l'ordre défini dans DefaultMaintenanceItems (même ordre que la page d'interface)
                final defaultItems = DefaultMaintenanceItems.getDefaultItems();
                final orderMap = <String, int>{};
                for (int i = 0; i < defaultItems.length; i++) {
                  orderMap[defaultItems[i].name] = i;
                }

                maintenanceItems.sort((a, b) {
                  final orderA = orderMap[a.name] ?? 999;
                  final orderB = orderMap[b.name] ?? 999;
                  return orderA.compareTo(orderB);
                });
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (maintenanceItems.isNotEmpty) ...[
                      Text(
                        'Entretiens',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...maintenanceItems.map((item) => 
                        _buildMaintenanceStatusRow(context, provider, item, vehicle)),
                    ],
                  ],
                );
              },
            ),
            
            const SizedBox(height: 12),
            
            // Documents actifs
            FutureBuilder<List<DocumentItem>>(
              future: provider.getDocumentItemsForVehicle(vehicle.id!),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const SizedBox(height: 20, child: LinearProgressIndicator());
                }
                
                final documentItems = snapshot.data!
                    .where((doc) => doc.isActive)
                    .toList();
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (documentItems.isNotEmpty) ...[
                      Text(
                        'Documents',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...documentItems.map((doc) => 
                        _buildDocumentStatusRow(context, doc)),
                    ],
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceStatusRow(
    BuildContext context,
    MaintenanceProvider provider,
    MaintenanceItem item,
    VehicleConfig vehicle,
  ) {
    final status = provider.getMaintenanceItemStatusForVehicle(item, vehicle);
    final remainingKm = status['remainingKm'] as int;
    final statusColor = status['statusColor'] as Color;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            _getIconForMaintenanceItem(item.name),
            size: 16,
            color: statusColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              item.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: 12,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: statusColor.withValues(alpha: 0.3)),
            ),
            child: Text(
              remainingKm > 0 ? 'Reste: ${remainingKm} km' : 'En retard: ${remainingKm.abs()} km',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentStatusRow(BuildContext context, DocumentItem document) {
    final daysUntilExpiration = document.daysUntilExpiration;
    Color statusColor;
    String statusText;

    if (daysUntilExpiration < 0) {
      statusColor = Colors.red;
      statusText = 'Expiré: depuis ${daysUntilExpiration.abs()} j';
    } else if (daysUntilExpiration <= 7) {
      statusColor = Colors.red;
      statusText = 'À expirer: reste ${daysUntilExpiration} j';
    } else if (daysUntilExpiration <= 30) {
      statusColor = Colors.orange;
      statusText = 'À expirer: reste ${daysUntilExpiration} j';
    } else {
      statusColor = Colors.green;
      statusText = 'Valide: reste ${daysUntilExpiration} j';
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            _getIconForDocumentCategory(document.category),
            size: 16,
            color: statusColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              document.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: 12,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: statusColor.withValues(alpha: 0.3)),
            ),
            child: Text(
              statusText,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Obtenir l'icône pour un entretien
  IconData _getIconForMaintenanceItem(String itemName) {
    final name = itemName.toLowerCase();
    if (name.contains('vidange') || name.contains('huile')) {
      return MdiIcons.oilLevel;
    } else if (name.contains('filtre')) {
      return MdiIcons.airFilter;
    } else if (name.contains('courroie')) {
      return MdiIcons.cog;
    } else if (name.contains('liquide')) {
      return MdiIcons.waterPercent;
    } else if (name.contains('plaquette') || name.contains('frein')) {
      return MdiIcons.carBrakeAlert;
    } else if (name.contains('pneu')) {
      return MdiIcons.carTireAlert;
    }
    return MdiIcons.wrench;
  }

  // Obtenir l'icône pour une catégorie de document
  IconData _getIconForDocumentCategory(String category) {
    switch (category.toLowerCase()) {
      case 'assurance':
        return MdiIcons.shield;
      case 'contrôle':
        return MdiIcons.clipboardCheck;
      case 'sécurité':
        return MdiIcons.fireExtinguisher;
      default:
        return MdiIcons.fileDocument;
    }
  }
}
