# Configuration du proxy pour Flutter
Write-Host "Configuration du proxy pour Flutter..." -ForegroundColor Green

# Définir les variables d'environnement pour la session actuelle
$env:HTTP_PROXY = "******************************@**********:8080"
$env:HTTPS_PROXY = "******************************@**********:8080"
$env:PUB_HOSTED_URL = "https://pub.dartlang.org"

# Définir les variables d'environnement de façon permanente
[Environment]::SetEnvironmentVariable("HTTP_PROXY", "******************************@**********:8080", "User")
[Environment]::SetEnvironmentVariable("HTTPS_PROXY", "******************************@**********:8080", "User")
[Environment]::SetEnvironmentVariable("PUB_HOSTED_URL", "https://pub.dartlang.org", "User")

Write-Host "Proxy configuré avec succès !" -ForegroundColor Green
Write-Host "Les variables d'environnement ont été définies pour la session actuelle et de façon permanente." -ForegroundColor Yellow

# Tester la configuration
Write-Host "Test de la configuration..." -ForegroundColor Blue
flutter pub get
