import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import '../services/theme_service.dart';
import '../services/import_export_service.dart';
import '../l10n/app_localizations.dart';
import '../screens/help_faq_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/about_screen.dart';

class ModernDrawer extends StatelessWidget {
  const ModernDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isTablet = screenWidth > 600;

    return Drawer(
      backgroundColor: Colors.white,
      elevation: 16,
      width: isTablet ? 320 : 280, // Responsive width
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // En-tête avec logo et nom de l'application
          _buildDrawerHeader(context, themeService, l10n),

          // Liste des éléments du menu
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                const SizedBox(height: 16),

                // Aide & FAQ
                _buildDrawerItem(
                  context,
                  icon: Icons.help_outline,
                  title: l10n.help,
                  subtitle: '',
                  color: themeService.primaryColor,
                  onTap: () => _navigateToHelpFaq(context),
                ),
                _buildDivider(),

                // Politique de confidentialité
                _buildDrawerItem(
                  context,
                  icon: Icons.privacy_tip_outlined,
                  title: l10n.privacyPolicy,
                  subtitle: '',
                  color: themeService.primaryColor,
                  onTap: () => _navigateToPrivacyPolicy(context),
                ),
                _buildDivider(),

                // À propos
                _buildDrawerItem(
                  context,
                  icon: Icons.info_outline,
                  title: l10n.about,
                  subtitle: '',
                  color: themeService.primaryColor,
                  onTap: () => _navigateToAbout(context),
                ),
                _buildDivider(),

                // Import/Export
                _buildDrawerItem(
                  context,
                  icon: Icons.import_export,
                  title: l10n.importExport,
                  subtitle: '',
                  color: themeService.primaryColor,
                  onTap: () => _showImportExport(context),
                ),
                _buildDivider(),

                // Mise à jour
                _buildDrawerItem(
                  context,
                  icon: Icons.system_update,
                  title: l10n.updateApp,
                  subtitle: '',
                  color: themeService.primaryColor,
                  onTap: () => _checkForUpdates(context),
                ),
                _buildDivider(),

                // Évaluation
                _buildDrawerItem(
                  context,
                  icon: Icons.star_outline,
                  title: l10n.rateApp,
                  subtitle: '',
                  color: themeService.primaryColor,
                  onTap: () => _openPlayStore(context),
                ),
                _buildDivider(),

                // Quitter
                _buildDrawerItem(
                  context,
                  icon: Icons.exit_to_app,
                  title: l10n.quit,
                  subtitle: '',
                  color: const Color(0xFFE53E3E),
                  onTap: () => _showExitConfirmation(context),
                ),
              ],
            ),
          ),

          // Pied de page avec version
          _buildDrawerFooter(context),
        ],
      ),
    );
      },
    );
  }

  Widget _buildDrawerHeader(BuildContext context, ThemeService themeService, AppLocalizations l10n) {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight > 700 ? 250.0 : 220.0;

    return Container(
      height: headerHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            themeService.primaryColor.withValues(alpha: 0.9), // Plus visible
            themeService.primaryColor.withValues(alpha: 0.7), // Dégradé
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Salutation simple
              Text(
                l10n.hello,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w300,
                  fontSize: 24,
                ),
              ),

              const SizedBox(height: 32),

              // Section avec logo officiel et informations
              Row(
                children: [
                  // Logo officiel CAROSTI uniquement
                  SizedBox(
                    width: 78,
                    height: 78,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(15),
                      child: Image.asset(
                        'assets/images/LogoMenuLat3.png',
                        width: 78,
                        height: 78,
                        fit: BoxFit.contain,
                        key: const ValueKey('logo_menu_lat_3'), // Force rebuild
                        errorBuilder: (context, error, stackTrace) {
                          // Erreur de chargement de LogoMenuLat3.png
                          return Container(
                            width: 78,
                            height: 78,
                            decoration: BoxDecoration(
                              color: themeService.primaryColor.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.directions_car,
                              size: 30,
                              color: themeService.primaryColor,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Informations de l'application sur deux lignes
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Carosti Auto',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'Carnet Entretien',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }





  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    bool hasSubmenu = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.of(context).pop(); // Fermer le drawer
            onTap();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
            child: Row(
              children: [
                // Icône minimisée
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 16),

                // Texte principal minimisé
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w400,
                      color: const Color(0xFF4A5568),
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Flèche minimisée
                Icon(
                  hasSubmenu ? Icons.keyboard_arrow_down : Icons.chevron_right,
                  color: const Color(0xFFA0AEC0),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 2),
      height: 0.5,
      color: const Color(0xFFE2E8F0),
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: FutureBuilder<String>(
        future: _getAppVersion(),
        builder: (context, snapshot) {
          final version = snapshot.data ?? '1.0.0';
          return Text(
            '${l10n.version} $version',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: const Color(0xFF718096),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          );
        },
      ),
    );
  }

  Future<String> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      return '1.0.0';
    }
  }

  // Méthodes de navigation et actions
  void _navigateToHelpFaq(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const HelpFaqScreen()),
    );
  }

  void _navigateToPrivacyPolicy(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PrivacyPolicyScreen()),
    );
  }

  void _navigateToAbout(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AboutScreen()),
    );
  }

  void _showImportExport(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.import_export,
                color: Colors.green,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                l10n.importExport,
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.download, color: Colors.green, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      '${l10n.exportData}: ${l10n.createBackup} (.dat)',
                      style: const TextStyle(fontSize: 13, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  const Icon(Icons.folder_open, color: Colors.blue, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      '${l10n.importData}: ${l10n.restoreFromBackup}',
                      style: const TextStyle(fontSize: 13, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                minimumSize: const Size(50, 28),
              ),
              child: const Text('Annuler', style: TextStyle(fontSize: 12)),
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        Navigator.of(context).pop();
                        await ImportExportService.importData(context: context);
                      },
                      icon: const Icon(Icons.folder_open, size: 12),
                      label: Text(l10n.importData, style: const TextStyle(fontSize: 12)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                        minimumSize: const Size(60, 28),
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        Navigator.of(context).pop();
                        await ImportExportService.exportData(context);
                      },
                      icon: const Icon(Icons.download, size: 12),
                      label: Text(l10n.exportData, style: const TextStyle(fontSize: 12)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                        minimumSize: const Size(60, 28),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _checkForUpdates(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.system_update,
                color: Colors.purple,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Mise à jour',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.refresh, color: Colors.blue, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Vérifier les mises à jour sur le Play Store',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  const Icon(Icons.star, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Nouvelles fonctionnalités et améliorations.',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  const Icon(Icons.phone_android, color: Colors.grey, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Voulez-vous ouvrir le Play Store ?',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                minimumSize: const Size(60, 32),
              ),
              child: const Text('Annuler', style: TextStyle(fontSize: 13)),
            ),
            ElevatedButton.icon(
              onPressed: () async {
                Navigator.of(context).pop();
                const url = 'https://play.google.com/store/apps/details?id=com.carosti.app';
                try {
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
                  }
                } catch (e) {
                  // Erreur silencieuse
                }
              },
              icon: const Icon(Icons.system_update, size: 14),
              label: const Text('Vérifier', style: TextStyle(fontSize: 13)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                minimumSize: const Size(80, 32),
              ),
            ),
          ],
        );
      },
    );
  }

  void _openPlayStore(BuildContext context) async {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.star,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                l10n.rateAppTitle,
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.star_outline, color: Colors.orange, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      l10n.enjoyingApp,
                      style: const TextStyle(fontSize: 13, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  const Icon(Icons.shop, color: Colors.green, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      l10n.rateOnStore,
                      style: const TextStyle(fontSize: 13, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                minimumSize: const Size(55, 28),
              ),
              child: Text(l10n.maybeLater, style: const TextStyle(fontSize: 12)),
            ),
            ElevatedButton.icon(
              onPressed: () async {
                Navigator.of(context).pop();
                const url = 'https://play.google.com/store/apps/details?id=com.carosti.app';
                try {
                  if (await canLaunchUrl(Uri.parse(url))) {
                    await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
                  }
                } catch (e) {
                  // Erreur silencieuse
                }
              },
              icon: const Icon(Icons.star, size: 12),
              label: Text(l10n.rateApp, style: const TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                minimumSize: const Size(70, 28),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showExitConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              const Icon(
                Icons.exit_to_app,
                color: Colors.red,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Quitter l\'application',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          content: const Text(
            'Êtes-vous sûr de vouloir quitter Carosti ?',
            style: TextStyle(fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                minimumSize: const Size(55, 28),
              ),
              child: const Text('Annuler', style: TextStyle(fontSize: 12)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                SystemNavigator.pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                minimumSize: const Size(70, 28),
              ),
              child: const Text('Quitter', style: TextStyle(fontSize: 12)),
            ),
          ],
        );
      },
    );
  }
}