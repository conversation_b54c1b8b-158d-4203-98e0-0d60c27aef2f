import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/vehicle_config.dart';
import 'vehicle_config_screen.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';
import '../widgets/shimmer_loading.dart';
import '../services/premium_service.dart';
import '../l10n/app_localizations.dart';
import 'global_status_screen.dart';

class VehicleManagementScreen extends StatelessWidget {
  const VehicleManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Mes Véhicules',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () => _navigateToGlobalStatus(context),
            icon: Icon(MdiIcons.viewDashboard),
            tooltip: 'État Global',
          ),
          IconButton(
            onPressed: () => _navigateToAddVehicle(context),
            icon: const Icon(Icons.add),
            tooltip: 'Ajouter un véhicule',
          ),
        ],
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.allVehicles.isEmpty) {
            return _buildEmptyState(context);
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: provider.allVehicles.length,
            itemBuilder: (context, index) {
              final vehicle = provider.allVehicles[index];
              final isCurrentVehicle = provider.currentVehicle?.id == vehicle.id;
              
              return _buildVehicleCard(
                context,
                vehicle,
                isCurrentVehicle,
                provider,
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddVehicle(context),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              MdiIcons.carOff,
              size: 40,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Aucun véhicule',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ajoutez votre premier véhicule pour commencer',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToAddVehicle(context),
            icon: const Icon(Icons.add),
            label: const Text('Ajouter un véhicule'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleCard(
    BuildContext context,
    VehicleConfig vehicle,
    bool isCurrentVehicle,
    MaintenanceProvider provider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        border: isCurrentVehicle
            ? Border.all(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              )
            : null,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ModernFadeIn(
        delay: const Duration(milliseconds: 200),
        child: ModernCard(
          padding: const EdgeInsets.all(16),
          backgroundColor: isCurrentVehicle
              ? Colors.green.withValues(alpha: 0.1)  // Vert clair pour véhicule actuel
              : Colors.grey.withValues(alpha: 0.1),   // Gris clair pour autres véhicules
          enableHoverEffect: true,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec nom et badge actuel
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      MdiIcons.car,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                vehicle.vehicleName,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            if (isCurrentVehicle)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.green),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.check_circle,
                                      color: Colors.green,
                                      size: 14,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Actuel',
                                      style: TextStyle(
                                        color: Colors.green,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 10,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${vehicle.brand} ${vehicle.model} (${vehicle.year})',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Informations du véhicule
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  children: [
                    _buildVehicleInfoRow(
                      MdiIcons.speedometer,
                      'Kilométrage',
                      '${vehicle.currentKilometers} km',
                      Colors.blue,
                    ),
                    if (vehicle.licensePlate != null) ...[
                      const SizedBox(height: 8),
                      _buildVehicleInfoRow(
                        MdiIcons.cardAccountDetails,
                        'Plaque',
                        vehicle.licensePlate!,
                        Colors.orange,
                      ),
                    ],
                    if (vehicle.engineType != null) ...[
                      const SizedBox(height: 8),
                      _buildVehicleInfoRow(
                        MdiIcons.engine,
                        'Moteur',
                        vehicle.engineType!,
                        Colors.purple,
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Boutons d'action
              Row(
                children: [
                  if (!isCurrentVehicle)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _switchToVehicle(context, vehicle, provider),
                        icon: const Icon(Icons.swap_horiz, size: 16),
                        label: const Text(
                          'Utiliser',
                          style: TextStyle(fontSize: 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                  if (!isCurrentVehicle) const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _editVehicle(context, vehicle),
                      icon: const Icon(Icons.edit, size: 16),
                      label: Text(
                        AppLocalizations.of(context).modify,
                        style: const TextStyle(fontSize: 12),
                      ),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: provider.allVehicles.length > 1
                        ? () => _deleteVehicle(context, vehicle, provider)
                        : null,
                    icon: Icon(
                      Icons.delete,
                      color: provider.allVehicles.length > 1
                          ? Colors.red
                          : Colors.grey,
                      size: 20,
                    ),
                    tooltip: provider.allVehicles.length > 1
                        ? AppLocalizations.of(context).deleteAction
                        : AppLocalizations.of(context).cannotDeleteLastVehicle,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVehicleInfoRow(
    IconData icon,
    String label,
    String value,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToAddVehicle(BuildContext context) async {
    final provider = context.read<MaintenanceProvider>();
    final premiumService = PremiumService();
    final currentVehicleCount = provider.allVehicles.length;

    // Vérifier si l'utilisateur peut ajouter un véhicule
    if (!premiumService.canAddVehicle(currentVehicleCount)) {
      // Afficher le dialog de limitation
      final shouldProceed = await premiumService.showLimitDialog(context);
      if (!shouldProceed) return;
    }

    // Procéder à l'ajout du véhicule
    if (context.mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const VehicleConfigScreen(isCreatingNew: true),
        ),
      );
    }
  }

  void _editVehicle(BuildContext context, VehicleConfig vehicle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const VehicleConfigScreen(),
      ),
    );
  }

  void _switchToVehicle(
    BuildContext context,
    VehicleConfig vehicle,
    MaintenanceProvider provider,
  ) async {
    try {
      await provider.switchToVehicle(vehicle);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).vehicleSelected} "${vehicle.vehicleName}"'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.of(context).errorMessage}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteVehicle(
    BuildContext context,
    VehicleConfig vehicle,
    MaintenanceProvider provider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text(
          '⚠️ Supprimer véhicule',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        content: Text(
          'Supprimer "${vehicle.vehicleName}" ?\n\n'
          '• Véhicule et configuration\n'
          '• Historique des entretiens\n'
          '• Toutes les données associées\n\n'
          'Action irréversible !',
          style: const TextStyle(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text(
              'Annuler',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await provider.deleteVehicle(vehicle);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.white, size: 20),
                          const SizedBox(width: 8),
                          Text('Véhicule "${vehicle.vehicleName}" supprimé'),
                        ],
                      ),
                      backgroundColor: Colors.green,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(Icons.error, color: Colors.white, size: 20),
                          const SizedBox(width: 8),
                          Text('Erreur: $e'),
                        ],
                      ),
                      backgroundColor: Colors.red,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Supprimer',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  // Navigation vers l'état global
  void _navigateToGlobalStatus(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const GlobalStatusScreen(),
      ),
    );
  }
}
