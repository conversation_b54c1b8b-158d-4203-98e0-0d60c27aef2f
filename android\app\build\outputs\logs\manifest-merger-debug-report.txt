-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:7:5-44:19
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
MERGED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-33:19
MERGED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-13:19
MERGED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-13:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b4bec413f45f01a07cb3ae9eb560838\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b4bec413f45f01a07cb3ae9eb560838\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d27ec31546b092d1a25e1529bc7d0d01\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d27ec31546b092d1a25e1529bc7d0d01\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6a19e01b7973872d56af53e8c61203\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6a19e01b7973872d56af53e8c61203\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdcabae2bf2167f483e278a2c8c545fd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdcabae2bf2167f483e278a2c8c545fd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2735f00474ea49f3aafe3ef399f39462\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2735f00474ea49f3aafe3ef399f39462\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eaf5face0b8ca07dd7669fdad8ee570\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eaf5face0b8ca07dd7669fdad8ee570\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d946368789e667402ea53bd4b56a62d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d946368789e667402ea53bd4b56a62d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8d3bb8c49e85787326c44e781274177\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8d3bb8c49e85787326c44e781274177\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:1:1-56:12
MERGED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:1:1-56:12
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:webview_flutter_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e115093ecfdcf00d62fda419f1b05b4\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b4bec413f45f01a07cb3ae9eb560838\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3afbe6dd2c3a497e6596084102e039dc\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dad6ecdc4610e29e4f98bd0a0d7ae39e\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\060efdb6a9751741ee78fbf8e46c9b9b\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fb97e251b4473c05b59cfc9f4d37022\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e97f996b3faf6ee519414126c2f22e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad14cafd770225a5ea4c409555f990b1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed5c0f41c04786c078cfdc46adaf64b7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:17:1-112:12
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ce907863b891ea131d8e26649a7de02\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d27ec31546b092d1a25e1529bc7d0d01\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6a19e01b7973872d56af53e8c61203\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdcabae2bf2167f483e278a2c8c545fd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2735f00474ea49f3aafe3ef399f39462\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eaf5face0b8ca07dd7669fdad8ee570\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c700a73c2e433e10f13879e3255c75cd\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\98f601b844e91efd1af3c340d1848126\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4116675e28f344e3bc6e9bb9e74eaffc\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\a47cdeb9700b5a96a1d4dbbad5d323c1\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2942010c9ed71003c7924ce9972450e1\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40fc1ee1789a68053845db0e6462952\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2eb114ef51fe57c13b1c6ef5513c1ac\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10dcbe1ac2635d350953adab3ef6eeee\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21b3fd2afee741f0a8ad2fe79d68bc95\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e92cbce7ffb19dceb25aec792eea0129\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bb57d876a83e989d190ac99bfb3e8b7b\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbd39f8fef9835621922864d80fb7b3\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1382f18e26e8d209895d5452e245d4d2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e0ec76ad385a39b8dc6383102a73c3c\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c75c169ac108775ed25d679bfd2b249b\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a82432f68aaeb6fdcda413b8f596f847\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8004596eeeb40a001b5eb68005ab7146\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\326f41f20e705ccb630bd6dafc323fc5\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb350bf13de012daab7eafa50b89f631\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\503bcc433efe2bc22ed5095c8585f737\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5d479b3084958c0d98402a430c5f7d8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde86f55e71b0dcf886c49030e3a1cf4\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77567a2562bd1c9f31de5db3b1360e4a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2a2fbb10f1c4f98ad3b7125d70ab54f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ac9d587e9c2842824378e1ecfbe6ac0\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a29948c2f253e1876593f81e72801329\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a07256f3d7124343d007ec775e251077\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13441e68197d27fe8cc92004c9f19292\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c13e242bb8252d25ba39d16c57d46dfa\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae4ec59404704d3c34556c9e0df2f65f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d946368789e667402ea53bd4b56a62d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b8eef1d561f0e22330dc55544d0d4c\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\587f11d5748568c4ec15d684dcb57f93\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\10902c07cd6b283d268111f0bb8cb34c\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ba6cff988bc34aa9ef53777992cb52\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb8cd01206bc9ff86737830aff65441\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8d3bb8c49e85787326c44e781274177\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58b5fd274f5af724457d8d63853325aa\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6aee64a3f72cfa3521eda93198562342\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de0d9422ef4a35cd8e217a913f9331c2\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ffb808291e50cac91d8ea467331e604\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f11a9fd79e6bcf48e60b5c061268ec2\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8973e7dff2f72a800d60a98975878e39\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\407460fcd8cc922aa38eb4482a74d00d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\82a0c6d804b230b267ac5bef212b9964\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7649ebb3447ddfca87908ace350b608\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\820d61c1b0442a8b4d36f5d78e13418a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:4:5-67
MERGED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:5:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:5:22-76
queries
ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:50:5-55:15
MERGED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
MERGED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-17:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:51:9-54:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:52:13-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:52:21-70
data
ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\main\AndroidManifest.xml:53:19-48
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
MERGED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\shared_preferences_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\connectivity_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e115093ecfdcf00d62fda419f1b05b4\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e115093ecfdcf00d62fda419f1b05b4\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b4bec413f45f01a07cb3ae9eb560838\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b4bec413f45f01a07cb3ae9eb560838\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3afbe6dd2c3a497e6596084102e039dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3afbe6dd2c3a497e6596084102e039dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dad6ecdc4610e29e4f98bd0a0d7ae39e\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dad6ecdc4610e29e4f98bd0a0d7ae39e\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\060efdb6a9751741ee78fbf8e46c9b9b\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\060efdb6a9751741ee78fbf8e46c9b9b\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fb97e251b4473c05b59cfc9f4d37022\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7fb97e251b4473c05b59cfc9f4d37022\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e97f996b3faf6ee519414126c2f22e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79e97f996b3faf6ee519414126c2f22e\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad14cafd770225a5ea4c409555f990b1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad14cafd770225a5ea4c409555f990b1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed5c0f41c04786c078cfdc46adaf64b7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed5c0f41c04786c078cfdc46adaf64b7\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ce907863b891ea131d8e26649a7de02\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ce907863b891ea131d8e26649a7de02\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d27ec31546b092d1a25e1529bc7d0d01\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d27ec31546b092d1a25e1529bc7d0d01\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6a19e01b7973872d56af53e8c61203\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6a19e01b7973872d56af53e8c61203\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdcabae2bf2167f483e278a2c8c545fd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdcabae2bf2167f483e278a2c8c545fd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2735f00474ea49f3aafe3ef399f39462\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2735f00474ea49f3aafe3ef399f39462\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eaf5face0b8ca07dd7669fdad8ee570\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eaf5face0b8ca07dd7669fdad8ee570\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c700a73c2e433e10f13879e3255c75cd\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c700a73c2e433e10f13879e3255c75cd\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\98f601b844e91efd1af3c340d1848126\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\98f601b844e91efd1af3c340d1848126\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4116675e28f344e3bc6e9bb9e74eaffc\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4116675e28f344e3bc6e9bb9e74eaffc\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\a47cdeb9700b5a96a1d4dbbad5d323c1\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\a47cdeb9700b5a96a1d4dbbad5d323c1\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2942010c9ed71003c7924ce9972450e1\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2942010c9ed71003c7924ce9972450e1\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40fc1ee1789a68053845db0e6462952\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40fc1ee1789a68053845db0e6462952\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2eb114ef51fe57c13b1c6ef5513c1ac\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a2eb114ef51fe57c13b1c6ef5513c1ac\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10dcbe1ac2635d350953adab3ef6eeee\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10dcbe1ac2635d350953adab3ef6eeee\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21b3fd2afee741f0a8ad2fe79d68bc95\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21b3fd2afee741f0a8ad2fe79d68bc95\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e92cbce7ffb19dceb25aec792eea0129\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e92cbce7ffb19dceb25aec792eea0129\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bb57d876a83e989d190ac99bfb3e8b7b\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bb57d876a83e989d190ac99bfb3e8b7b\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbd39f8fef9835621922864d80fb7b3\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5cbd39f8fef9835621922864d80fb7b3\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1382f18e26e8d209895d5452e245d4d2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1382f18e26e8d209895d5452e245d4d2\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e0ec76ad385a39b8dc6383102a73c3c\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8e0ec76ad385a39b8dc6383102a73c3c\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c75c169ac108775ed25d679bfd2b249b\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c75c169ac108775ed25d679bfd2b249b\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a82432f68aaeb6fdcda413b8f596f847\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a82432f68aaeb6fdcda413b8f596f847\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8004596eeeb40a001b5eb68005ab7146\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8004596eeeb40a001b5eb68005ab7146\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\326f41f20e705ccb630bd6dafc323fc5\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\326f41f20e705ccb630bd6dafc323fc5\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb350bf13de012daab7eafa50b89f631\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb350bf13de012daab7eafa50b89f631\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\503bcc433efe2bc22ed5095c8585f737\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\503bcc433efe2bc22ed5095c8585f737\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5d479b3084958c0d98402a430c5f7d8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c5d479b3084958c0d98402a430c5f7d8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde86f55e71b0dcf886c49030e3a1cf4\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fde86f55e71b0dcf886c49030e3a1cf4\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77567a2562bd1c9f31de5db3b1360e4a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\77567a2562bd1c9f31de5db3b1360e4a\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2a2fbb10f1c4f98ad3b7125d70ab54f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2a2fbb10f1c4f98ad3b7125d70ab54f\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ac9d587e9c2842824378e1ecfbe6ac0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ac9d587e9c2842824378e1ecfbe6ac0\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a29948c2f253e1876593f81e72801329\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a29948c2f253e1876593f81e72801329\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a07256f3d7124343d007ec775e251077\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a07256f3d7124343d007ec775e251077\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13441e68197d27fe8cc92004c9f19292\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13441e68197d27fe8cc92004c9f19292\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c13e242bb8252d25ba39d16c57d46dfa\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c13e242bb8252d25ba39d16c57d46dfa\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae4ec59404704d3c34556c9e0df2f65f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae4ec59404704d3c34556c9e0df2f65f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d946368789e667402ea53bd4b56a62d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d946368789e667402ea53bd4b56a62d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b8eef1d561f0e22330dc55544d0d4c\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\72b8eef1d561f0e22330dc55544d0d4c\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\587f11d5748568c4ec15d684dcb57f93\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\587f11d5748568c4ec15d684dcb57f93\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\10902c07cd6b283d268111f0bb8cb34c\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\10902c07cd6b283d268111f0bb8cb34c\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ba6cff988bc34aa9ef53777992cb52\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ba6cff988bc34aa9ef53777992cb52\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb8cd01206bc9ff86737830aff65441\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eb8cd01206bc9ff86737830aff65441\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8d3bb8c49e85787326c44e781274177\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8d3bb8c49e85787326c44e781274177\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58b5fd274f5af724457d8d63853325aa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58b5fd274f5af724457d8d63853325aa\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6aee64a3f72cfa3521eda93198562342\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6aee64a3f72cfa3521eda93198562342\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de0d9422ef4a35cd8e217a913f9331c2\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de0d9422ef4a35cd8e217a913f9331c2\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ffb808291e50cac91d8ea467331e604\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ffb808291e50cac91d8ea467331e604\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f11a9fd79e6bcf48e60b5c061268ec2\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f11a9fd79e6bcf48e60b5c061268ec2\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8973e7dff2f72a800d60a98975878e39\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8973e7dff2f72a800d60a98975878e39\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\407460fcd8cc922aa38eb4482a74d00d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\407460fcd8cc922aa38eb4482a74d00d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\82a0c6d804b230b267ac5bef212b9964\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\82a0c6d804b230b267ac5bef212b9964\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7649ebb3447ddfca87908ace350b608\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a7649ebb3447ddfca87908ace350b608\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\820d61c1b0442a8b4d36f5d78e13418a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\820d61c1b0442a8b4d36f5d78e13418a\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e115093ecfdcf00d62fda419f1b05b4\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\android\app\src\debug\AndroidManifest.xml
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:25-62
meta-data#io.flutter.embedded_views_preview
ADDED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-12:36
	android:value
		ADDED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-33
	android:name
		ADDED from [:google_mobile_ads] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\google_mobile_ads\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-61
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-35
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-64
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:21-69
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Desktop\AMEUR APP\mycar_maintenance\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\292a679d649b8a58fe89418be2f6dc59\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6a19e01b7973872d56af53e8c61203\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6a19e01b7973872d56af53e8c61203\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:41:23-71
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
property
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:107:9-109:62
	android:resource
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:109:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a796bc1946da6b7a95ec63ccd9e2a134\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:108:13-65
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f200f4a28092d3753dbbadd5663700bb\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec1fc45f181e6df95811b719c3bb94d\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\80b747fd0bf560516ae3aa3beeee4d5c\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbabf0ea45172bd7980421c97e59c5af\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d946368789e667402ea53bd4b56a62d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d946368789e667402ea53bd4b56a62d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a8d89e27875daab33c524cac617c2ba\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0069ea5816834cd17d18ef473de998b9\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.carosti.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.carosti.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1ac570f9a9ce8a2badd3127fcdbc117\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b44670365abd2c7e9b0fc7bbf4780580\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\c031f24f64cdf25efb90e1e8720e6cda\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
