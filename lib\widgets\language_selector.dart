import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/localization_service.dart';
import '../l10n/app_localizations.dart';

class LanguageSelector extends StatelessWidget {
  final bool showTitle;
  final bool isInDrawer;

  const LanguageSelector({
    super.key,
    this.showTitle = true,
    this.isInDrawer = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, child) {
        final l10n = AppLocalizations.of(context);
        final availableLanguages = localizationService.getAvailableLanguages();
        final currentLanguage = localizationService.languageCode;

        if (isInDrawer) {
          return ListTile(
            leading: const Icon(Icons.language),
            title: Text(l10n.language),
            subtitle: Text(localizationService.getCurrentLanguageName()),
            onTap: () => _showLanguageDialog(context, localizationService, availableLanguages),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitle) ...[
              Text(
                l10n.language,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
            ],
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: currentLanguage,
                  isExpanded: true,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  items: availableLanguages.map((language) {
                    return DropdownMenuItem<String>(
                      value: language['code'],
                      child: Row(
                        children: [
                          _getLanguageFlag(language['code']!),
                          const SizedBox(width: 12),
                          Text(
                            language['nativeName']!,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (String? newLanguage) {
                    if (newLanguage != null && newLanguage != currentLanguage) {
                      localizationService.changeLanguage(newLanguage);
                    }
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showLanguageDialog(
    BuildContext context,
    LocalizationService localizationService,
    List<Map<String, String>> availableLanguages,
  ) {
    final l10n = AppLocalizations.of(context);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n.language),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: availableLanguages.map((language) {
              final isSelected = language['code'] == localizationService.languageCode;
              
              return ListTile(
                leading: _getLanguageFlag(language['code']!),
                title: Text(language['nativeName']!),
                trailing: isSelected ? const Icon(Icons.check, color: Colors.blue) : null,
                onTap: () {
                  if (!isSelected) {
                    localizationService.changeLanguage(language['code']!);
                  }
                  Navigator.of(context).pop();
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.cancel),
            ),
          ],
        );
      },
    );
  }

  Widget _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return Container(
          width: 24,
          height: 18,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            border: Border.all(color: Colors.grey.shade300, width: 0.5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: Row(
              children: [
                Expanded(
                  child: Container(color: const Color(0xFF0055A4)),
                ),
                Expanded(
                  child: Container(color: Colors.white),
                ),
                Expanded(
                  child: Container(color: const Color(0xFFEF4135)),
                ),
              ],
            ),
          ),
        );
      case 'ar':
        return Container(
          width: 24,
          height: 18,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            border: Border.all(color: Colors.grey.shade300, width: 0.5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(2),
            child: Stack(
              children: [
                // Drapeau de l'Algérie - moitié verte et moitié blanche
                Row(
                  children: [
                    Expanded(
                      child: Container(color: const Color(0xFF006233)), // Vert algérien
                    ),
                    Expanded(
                      child: Container(color: Colors.white),
                    ),
                  ],
                ),
                // Croissant et étoile rouge au centre
                Center(
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: const Color(0xFFD21034), // Rouge algérien
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return const Icon(Icons.language, size: 24);
    }
  }
}

class LanguageSelectorTile extends StatelessWidget {
  const LanguageSelectorTile({super.key});

  @override
  Widget build(BuildContext context) {
    return const LanguageSelector(
      showTitle: false,
      isInDrawer: true,
    );
  }
}