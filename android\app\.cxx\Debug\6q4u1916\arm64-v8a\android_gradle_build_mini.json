{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\AMEUR APP\\mycar_maintenance\\android\\app\\.cxx\\Debug\\6q4u1916\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\AMEUR APP\\mycar_maintenance\\android\\app\\.cxx\\Debug\\6q4u1916\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}