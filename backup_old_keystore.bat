@echo off
echo ========================================
echo   SAUVEGARDE ANCIENNE CONFIGURATION
echo ========================================
echo.

REM Créer un dossier de sauvegarde avec timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

set BACKUP_DIR=backup_keystore_%datestamp%
mkdir "%BACKUP_DIR%"

echo 📁 Création du dossier de sauvegarde: %BACKUP_DIR%
echo.

REM Sauvegarder les fichiers existants
if exist "key.properties" (
    echo 💾 Sauvegarde de key.properties...
    copy "key.properties" "%BACKUP_DIR%\key.properties.bak"
)

if exist "android\key.properties" (
    echo 💾 Sauvegarde de android\key.properties...
    copy "android\key.properties" "%BACKUP_DIR%\android_key.properties.bak"
)

if exist "google-play-compatible.jks" (
    echo 💾 Sauvegarde de google-play-compatible.jks...
    copy "google-play-compatible.jks" "%BACKUP_DIR%\google-play-compatible.jks.bak"
)

if exist "android\carosti-release-key.jks" (
    echo 💾 Sauvegarde de android\carosti-release-key.jks...
    copy "android\carosti-release-key.jks" "%BACKUP_DIR%\carosti-release-key.jks.bak"
)

echo.
echo ✅ Sauvegarde terminée dans: %BACKUP_DIR%
echo.
echo 📋 Fichiers sauvegardés:
dir "%BACKUP_DIR%" /b
echo.
pause
