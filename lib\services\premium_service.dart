import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'billing_service.dart';

/// Service pour gérer les fonctionnalités premium
class PremiumService {
  static final PremiumService _instance = PremiumService._internal();
  factory PremiumService() => _instance;
  PremiumService._internal();

  static const String _premiumKey = 'is_premium_user';
  static const String _purchaseDateKey = 'premium_purchase_date';
  static const int _maxFreeVehicles = 3;
  static const double _premiumPrice = 5.0;

  bool _isPremium = false;
  DateTime? _purchaseDate;
  bool _isInitialized = false;

  // Service de billing
  final BillingService _billingService = BillingService();

  // Stream controller pour notifier les changements de statut premium
  final StreamController<bool> _premiumStatusController = StreamController<bool>.broadcast();
  Stream<bool> get premiumStatusStream => _premiumStatusController.stream;

  /// Initialise le service premium
  Future<void> initialize() async {
    try {
      debugPrint('💎 Initialisation service Premium...');

      // Initialiser le service de billing
      final billingInitialized = await _billingService.initialize();
      debugPrint('🛒 Billing service: ${billingInitialized ? 'OK' : 'Échec'}');

      // Configurer les callbacks du billing
      _billingService.onPurchaseComplete = _onPurchaseComplete;
      _billingService.onPurchaseError = _onPurchaseError;

      final prefs = await SharedPreferences.getInstance();
      _isPremium = prefs.getBool(_premiumKey) ?? false;

      final purchaseDateString = prefs.getString(_purchaseDateKey);
      if (purchaseDateString != null) {
        _purchaseDate = DateTime.parse(purchaseDateString);
      }

      // Vérifier les achats existants si le billing est disponible
      if (billingInitialized) {
        await _billingService.hasPurchasedPremium();
      }

      _isInitialized = true;
      debugPrint('💎 Service Premium initialisé - Statut: ${_isPremium ? "Premium" : "Gratuit"}');

      // Notifier le statut initial
      _premiumStatusController.add(_isPremium);
      
    } catch (e) {
      debugPrint('❌ Erreur initialisation service Premium: $e');
      _isInitialized = false;
    }
  }

  /// Callback appelé quand un achat est terminé
  void _onPurchaseComplete(bool success, String? error) {
    if (success) {
      debugPrint('🎉 Achat Premium réussi !');
      _activatePremium().then((_) {
        debugPrint('✅ Premium activé avec succès');
        // Le dialog de succès sera affiché par l'interface utilisateur
        // via l'écoute du stream premiumStatusStream
      });
    } else {
      debugPrint('❌ Achat Premium échoué: $error');
    }
  }

  /// Callback appelé en cas d'erreur d'achat
  void _onPurchaseError(String error) {
    debugPrint('❌ Erreur achat Premium: $error');
  }

  /// Vérifie si l'utilisateur peut ajouter un nouveau véhicule
  bool canAddVehicle(int currentVehicleCount) {
    if (_isPremium) return true;
    return currentVehicleCount < _maxFreeVehicles;
  }

  /// Obtient le nombre maximum de véhicules autorisés
  int get maxVehicles => _isPremium ? 999 : _maxFreeVehicles;

  /// Vérifie si l'utilisateur a atteint la limite gratuite
  bool hasReachedFreeLimit(int currentVehicleCount) {
    return !_isPremium && currentVehicleCount >= _maxFreeVehicles;
  }

  /// Affiche le dialog de limitation pour les utilisateurs gratuits
  Future<bool> showLimitDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.star,
                color: Colors.amber,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text('Limite atteinte'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Vous avez atteint la limite de $_maxFreeVehicles véhicules gratuits.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.star, color: Colors.amber, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Carosti Premium',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.amber[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Véhicules illimités\n• Suppression des publicités\n• Fonctionnalités avancées',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Prix unique: $premiumPrice',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.amber[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: const Text(
                      'Annuler',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.amber,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: const Text(
                      'Passer Premium',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
    
    if (result == true) {
      return await _processPremiumPurchase(context);
    }
    
    return false;
  }

  /// Simule l'achat premium (à remplacer par l'intégration réelle)
  Future<bool> _processPremiumPurchase(BuildContext context) async {
    try {
      debugPrint('💎 Traitement achat Premium...');
      
      // Afficher un dialog de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Traitement de l\'achat...'),
            ],
          ),
        ),
      );
      
      // Lancer l'achat via Google Play Billing
      final bool purchaseInitiated = await _billingService.purchasePremium();

      // Fermer le dialog de chargement
      if (context.mounted) Navigator.of(context).pop();

      if (!purchaseInitiated) {
        debugPrint('❌ Impossible de lancer l\'achat');
        if (context.mounted) {
          _showErrorDialog(context);
        }
        return false;
      }

      debugPrint('🛒 Achat lancé, en attente de la réponse Google Play...');
      // Le résultat sera géré par les callbacks _onPurchaseComplete/_onPurchaseError
      
      return true;
    } catch (e) {
      debugPrint('❌ Erreur achat Premium: $e');
      
      // Fermer le dialog de chargement si ouvert
      if (context.mounted) Navigator.of(context).pop();
      
      // Afficher l'erreur
      if (context.mounted) {
        _showErrorDialog(context);
      }
      
      return false;
    }
  }

  /// Active le statut premium
  Future<void> _activatePremium() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_premiumKey, true);
      await prefs.setString(_purchaseDateKey, DateTime.now().toIso8601String());
      
      _isPremium = true;
      _purchaseDate = DateTime.now();
      
      // Notifier le changement
      _premiumStatusController.add(_isPremium);
      
      debugPrint('✅ Premium activé avec succès');
    } catch (e) {
      debugPrint('❌ Erreur activation Premium: $e');
      throw e;
    }
  }

  /// Affiche le dialog d'erreur
  void _showErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.settings, color: Colors.orange, size: 20),
            const SizedBox(width: 6),
            const Flexible(
              child: Text(
                'Config requise',
                style: TextStyle(fontSize: 16),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        content: const Text(
          'Le paiement nécessite une configuration Google Play Console.\n\n'
          'Configurez le produit Premium pour activer les achats.',
          style: TextStyle(fontSize: 14),
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  child: const Text(
                    'Compris',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Affiche le dialog de succès
  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            const SizedBox(width: 12),
            const Text('Achat réussi !'),
          ],
        ),
        content: const Text(
          'Félicitations ! Vous êtes maintenant un utilisateur Premium.\n\nVous pouvez ajouter autant de véhicules que vous le souhaitez.',
        ),
        actions: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  child: const Text(
                    'Parfait !',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Restaure les achats (pour iOS principalement)
  Future<bool> restorePurchases() async {
    try {
      debugPrint('🔄 Restauration des achats...');
      // Ici vous intégreriez la restauration des achats
      // Pour la démo, on vérifie juste les préférences locales
      return _isPremium;
    } catch (e) {
      debugPrint('❌ Erreur restauration achats: $e');
      return false;
    }
  }

  /// Getters
  bool get isPremium => _isPremium;
  bool get isInitialized => _isInitialized;
  DateTime? get purchaseDate => _purchaseDate;
  int get maxFreeVehicles => _maxFreeVehicles;
  /// Obtient le prix du premium depuis Google Play ou valeur par défaut
  String get premiumPrice {
    final String? playStorePrice = _billingService.getPremiumPrice();
    if (playStorePrice != null) {
      return playStorePrice;
    }
    return '${_premiumPrice.toStringAsFixed(2)} €';
  }

  /// Obtient le prix par défaut (pour compatibilité)
  double get premiumPriceValue => _premiumPrice;

  /// Dispose les ressources
  void dispose() {
    _premiumStatusController.close();
  }
}
