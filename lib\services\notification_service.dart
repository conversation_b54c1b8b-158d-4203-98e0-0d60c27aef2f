import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../models/maintenance_item.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Simuler un système de notifications
  final List<MaintenanceNotification> _notifications = [];

  List<MaintenanceNotification> get notifications => _notifications;

  void generateNotifications(List<MaintenanceItem> items) {
    _notifications.clear();

    for (final item in items) {
      // Ignorer complètement les opérations de type "Autre"
      if (item.category.toLowerCase() == 'autre') {
        continue; // Passer à l'élément suivant sans générer de notification
      }

      if (item.isMaintenanceDue) {
        _notifications.add(
          MaintenanceNotification(
            id: 'urgent_${item.id}',
            title: 'Entretien urgent requis',
            message:
                '${item.name} est en retard de ${item.kmUntilNextMaintenance.abs()} km',
            type: NotificationType.urgent,
            maintenanceItem: item,
            createdAt: DateTime.now(),
          ),
        );
      } else if (item.isMaintenanceApproaching) {
        _notifications.add(
          MaintenanceNotification(
            id: 'approaching_${item.id}',
            title: 'Entretien à prévoir',
            message: '${item.name} dans ${item.kmUntilNextMaintenance} km',
            type: NotificationType.reminder,
            maintenanceItem: item,
            createdAt: DateTime.now(),
          ),
        );
      }
    }
  }

  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    }
  }

  void dismissNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
  }

  void addNotification(MaintenanceNotification notification) {
    // Éviter les doublons
    _notifications.removeWhere((n) => n.id == notification.id);
    _notifications.add(notification);
  }

  void clearAllNotifications() {
    _notifications.clear();
  }

  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  List<MaintenanceNotification> get urgentNotifications =>
      _notifications.where((n) => n.type == NotificationType.urgent).toList();

  List<MaintenanceNotification> get reminderNotifications =>
      _notifications.where((n) => n.type == NotificationType.reminder).toList();
}

enum NotificationType { urgent, reminder, info }

class MaintenanceNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final MaintenanceItem maintenanceItem;
  final DateTime createdAt;
  final bool isRead;

  MaintenanceNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.maintenanceItem,
    required this.createdAt,
    this.isRead = false,
  });

  MaintenanceNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    MaintenanceItem? maintenanceItem,
    DateTime? createdAt,
    bool? isRead,
  }) {
    return MaintenanceNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      maintenanceItem: maintenanceItem ?? this.maintenanceItem,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
    );
  }

  Color get color {
    switch (type) {
      case NotificationType.urgent:
        return Colors.red;
      case NotificationType.reminder:
        return Colors.orange;
      case NotificationType.info:
        return Colors.blue;
    }
  }

  IconData get icon {
    // Utiliser l'icône spécifique de l'opération de maintenance
    return _getIconForMaintenanceItem(maintenanceItem.name);
  }

  // Obtenir l'icône spécifique pour chaque entretien
  IconData _getIconForMaintenanceItem(String maintenanceName) {
    switch (maintenanceName.toLowerCase()) {
      case 'vidange huile moteur':
        return MdiIcons.oilLevel;
      case 'chaîne de distribution':
        return MdiIcons.linkVariant;
      case 'filtre à air':
        return MdiIcons.airFilter;
      case 'filtre à gasoil':
        return MdiIcons.gasStation;
      case 'filtre habitacle':
        return MdiIcons.airConditioner;
      case 'plaquettes de frein':
        return MdiIcons.carBrakeAlert;
      case 'liquide de frein':
        return MdiIcons.carBrakeRetarder;
      case 'pneus':
        return MdiIcons.carTireAlert;
      case 'liquide de refroidissement':
        return MdiIcons.radiator;
      case 'courroie d\'accessoires':
        return MdiIcons.carCruiseControl;
      default:
        return MdiIcons.wrench;
    }
  }
}
