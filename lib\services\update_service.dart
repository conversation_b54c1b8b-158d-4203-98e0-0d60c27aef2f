import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../l10n/app_localizations.dart';

/// Service pour gérer les mises à jour de l'application
class UpdateService {
  static final UpdateService _instance = UpdateService._internal();
  factory UpdateService() => _instance;
  UpdateService._internal();

  static const String _playStoreUrl = 'https://play.google.com/store/apps/details?id=com.carosti.app';
  static const String _appStoreUrl = 'https://apps.apple.com/app/carosti/id123456789'; // À remplacer par l'ID réel
  
  bool _isCheckingUpdate = false;
  String? _latestVersion;
  String? _currentVersion;

  /// Vérifie s'il y a une mise à jour disponible
  Future<bool> checkForUpdate() async {
    if (_isCheckingUpdate) return false;
    
    try {
      _isCheckingUpdate = true;
      debugPrint('🔄 Vérification des mises à jour...');
      
      // Obtenir la version actuelle
      final packageInfo = await PackageInfo.fromPlatform();
      _currentVersion = packageInfo.version;
      
      // Vérifier la dernière version disponible
      _latestVersion = await _getLatestVersion();
      
      if (_latestVersion != null && _currentVersion != null) {
        final isUpdateAvailable = _isVersionNewer(_latestVersion!, _currentVersion!);
        debugPrint('📱 Version actuelle: $_currentVersion');
        debugPrint('🆕 Dernière version: $_latestVersion');
        debugPrint('🔄 Mise à jour disponible: $isUpdateAvailable');
        
        return isUpdateAvailable;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ Erreur lors de la vérification des mises à jour: $e');
      return false;
    } finally {
      _isCheckingUpdate = false;
    }
  }

  /// Obtient la dernière version depuis le store
  Future<String?> _getLatestVersion() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidLatestVersion();
      } else if (Platform.isIOS) {
        return await _getIOSLatestVersion();
      }
      return null;
    } catch (e) {
      debugPrint('❌ Erreur lors de la récupération de la version: $e');
      return null;
    }
  }

  /// Obtient la dernière version Android depuis Google Play
  Future<String?> _getAndroidLatestVersion() async {
    try {
      // Utiliser l'API Google Play Store (nécessite une configuration côté serveur)
      // Pour une implémentation simple, on peut utiliser un endpoint personnalisé
      // ou parser la page du Play Store (moins fiable)
      
      // Exemple avec un endpoint personnalisé (recommandé)
      final response = await http.get(
        Uri.parse('https://api.carosti.com/version/android'), // À remplacer par votre API
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['version'] as String?;
      }
      
      // Fallback : version par défaut si l'API n'est pas disponible
      return null;
    } catch (e) {
      debugPrint('❌ Erreur récupération version Android: $e');
      return null;
    }
  }

  /// Obtient la dernière version iOS depuis l'App Store
  Future<String?> _getIOSLatestVersion() async {
    try {
      // Utiliser l'API iTunes Search pour iOS
      final response = await http.get(
        Uri.parse('https://itunes.apple.com/lookup?bundleId=com.carosti.app'),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final results = data['results'] as List?;
        if (results != null && results.isNotEmpty) {
          return results[0]['version'] as String?;
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ Erreur récupération version iOS: $e');
      return null;
    }
  }

  /// Compare deux versions pour déterminer si une mise à jour est nécessaire
  bool _isVersionNewer(String latestVersion, String currentVersion) {
    try {
      final latest = latestVersion.split('.').map(int.parse).toList();
      final current = currentVersion.split('.').map(int.parse).toList();
      
      // Normaliser les listes à la même longueur
      while (latest.length < current.length) latest.add(0);
      while (current.length < latest.length) current.add(0);
      
      for (int i = 0; i < latest.length; i++) {
        if (latest[i] > current[i]) return true;
        if (latest[i] < current[i]) return false;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ Erreur comparaison versions: $e');
      return false;
    }
  }

  /// Affiche le dialog de mise à jour
  Future<void> showUpdateDialog(BuildContext context) async {
    if (_latestVersion == null || _currentVersion == null) return;

    final l10n = AppLocalizations.of(context);
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.system_update,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Text(l10n.updateAvailable),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.newVersionAvailable,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Text(
                    '${l10n.currentVersionText}: ',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    _currentVersion!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    '${l10n.latestVersionText}: ',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    _latestVersion!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                l10n.improvements,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: Text(
                      l10n.updateLater,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _openStore();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    child: Text(
                      l10n.updateNow,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Ouvre le store approprié pour la mise à jour
  Future<void> _openStore() async {
    try {
      final url = Platform.isAndroid ? _playStoreUrl : _appStoreUrl;
      final uri = Uri.parse(url);
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('❌ Impossible d\'ouvrir le store');
      }
    } catch (e) {
      debugPrint('❌ Erreur ouverture store: $e');
    }
  }

  /// Getters pour les versions
  String? get currentVersion => _currentVersion;
  String? get latestVersion => _latestVersion;
  bool get isCheckingUpdate => _isCheckingUpdate;
}
