import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

/// Helper class pour faciliter l'accès aux traductions
class LocalizationHelper {
  /// Obtenir les traductions depuis le contexte
  static AppLocalizations of(BuildContext context) {
    return AppLocalizations.of(context);
  }

  /// Vérifier si la langue actuelle est RTL
  static bool isRTL(BuildContext context) {
    return Directionality.of(context) == TextDirection.rtl;
  }

  /// Obtenir la direction du texte
  static TextDirection getTextDirection(BuildContext context) {
    return Directionality.of(context);
  }

  /// Formater un nombre selon la locale
  static String formatNumber(BuildContext context, num number) {
    // Pour l'arabe, on peut utiliser les chiffres arabes si nécessaire
    if (isRTL(context)) {
      return number.toString();
    }
    return number.toString();
  }

  /// Formater une date selon la locale
  static String formatDate(BuildContext context, DateTime date) {
    // Format français : dd/MM/yyyy
    // Format arabe : dd/MM/yyyy (même format mais RTL)
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year.toString();
    
    return '$day/$month/$year';
  }

  /// Formater une heure selon la locale
  static String formatTime(BuildContext context, TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    
    return '$hour:$minute';
  }

  /// Obtenir le nom du mois selon la locale
  static String getMonthName(BuildContext context, int month) {
    final l10n = of(context);
    
    if (isRTL(context)) {
      // Noms des mois en arabe
      const arabicMonths = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
      ];
      return arabicMonths[month - 1];
    } else {
      // Noms des mois en français
      const frenchMonths = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
      ];
      return frenchMonths[month - 1];
    }
  }

  /// Obtenir le nom du jour selon la locale
  static String getDayName(BuildContext context, int weekday) {
    if (isRTL(context)) {
      // Noms des jours en arabe (1 = Lundi)
      const arabicDays = [
        'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
      ];
      return arabicDays[weekday - 1];
    } else {
      // Noms des jours en français (1 = Lundi)
      const frenchDays = [
        'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'
      ];
      return frenchDays[weekday - 1];
    }
  }

  /// Formater une devise selon la locale
  static String formatCurrency(BuildContext context, double amount, String currencySymbol) {
    final formattedAmount = amount.toStringAsFixed(2);
    
    if (isRTL(context)) {
      // Format arabe : المبلغ الرمز
      return '$formattedAmount $currencySymbol';
    } else {
      // Format français : المبلغ الرمز
      return '$formattedAmount $currencySymbol';
    }
  }

  /// Obtenir le texte d'alignement selon la direction
  static TextAlign getTextAlign(BuildContext context, {TextAlign? defaultAlign}) {
    if (defaultAlign != null) return defaultAlign;
    
    return isRTL(context) ? TextAlign.right : TextAlign.left;
  }

  /// Obtenir l'alignement des widgets selon la direction
  static MainAxisAlignment getMainAxisAlignment(BuildContext context) {
    return isRTL(context) ? MainAxisAlignment.end : MainAxisAlignment.start;
  }

  /// Obtenir l'alignement croisé selon la direction
  static CrossAxisAlignment getCrossAxisAlignment(BuildContext context) {
    return isRTL(context) ? CrossAxisAlignment.end : CrossAxisAlignment.start;
  }
}