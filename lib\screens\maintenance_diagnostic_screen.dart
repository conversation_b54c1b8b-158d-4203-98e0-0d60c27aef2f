import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_item.dart';
import '../models/maintenance_history.dart';
import '../models/document_item.dart';
import '../models/default_maintenance_items.dart';
import 'kilometer_input_screen.dart';
import 'maintenance_detail_screen.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_animations.dart';
import '../l10n/app_localizations.dart';
import '../services/translation_service.dart';

class MaintenanceDiagnosticScreen extends StatefulWidget {
  final String? initialFilter; // Filtre initial à appliquer
  final bool scrollToDocuments; // Paramètre pour scroller vers la section documents
  final String? focusItemId; // ID de l'entretien à mettre en focus

  const MaintenanceDiagnosticScreen({
    super.key,
    this.initialFilter,
    this.scrollToDocuments = false,
    this.focusItemId,
  });

  @override
  State<MaintenanceDiagnosticScreen> createState() =>
      _MaintenanceDiagnosticScreenState();
}

class _MaintenanceDiagnosticScreenState
    extends State<MaintenanceDiagnosticScreen> {
  String _selectedFilter = 'Tous'; // Filtre sélectionné
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _documentsKey = GlobalKey();
  final Map<String, GlobalKey> _itemKeys = {}; // Clés pour chaque entretien

  @override
  void initState() {
    super.initState();
    // Initialiser le filtre avec la valeur passée en paramètre
    if (widget.initialFilter != null) {
      _selectedFilter = widget.initialFilter!;
    }

    // Si on doit scroller vers les documents, le faire après le build avec un délai
    if (widget.scrollToDocuments) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(const Duration(milliseconds: 300), () {
          _scrollToDocuments();
        });
      });
    }

    // Si on doit faire défiler vers un entretien spécifique
    if (widget.focusItemId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(const Duration(milliseconds: 500), () {
          _scrollToItem(widget.focusItemId!);
        });
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Méthode pour scroller vers la section documents
  void _scrollToDocuments() {
    if (_documentsKey.currentContext != null) {
      Scrollable.ensureVisible(
        _documentsKey.currentContext!,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeInOut,
      );
    } else {
      // Essayer avec un délai plus long si le context n'est pas encore disponible
      Future.delayed(const Duration(milliseconds: 200), () {
        if (_documentsKey.currentContext != null) {
          Scrollable.ensureVisible(
            _documentsKey.currentContext!,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  // Méthode pour scroller vers un entretien spécifique
  void _scrollToItem(String itemId) {
    final key = _itemKeys[itemId];
    if (key?.currentContext != null) {
      Scrollable.ensureVisible(
        key!.currentContext!,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeInOut,
        alignment: 0.1, // Position la carte vers le haut de l'écran
      );
    } else {
      // Essayer avec un délai plus long si le context n'est pas encore disponible
      Future.delayed(const Duration(milliseconds: 300), () {
        final key = _itemKeys[itemId];
        if (key?.currentContext != null) {
          Scrollable.ensureVisible(
            key!.currentContext!,
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeInOut,
            alignment: 0.1, // Position la carte vers le haut de l'écran
          );
        }
      });
    }
  }

  // Filtrer les éléments selon l'état sélectionné
  List<MaintenanceItem> _filterMaintenanceItems(
    List<MaintenanceItem> items,
    MaintenanceProvider provider,
  ) {
    // Exclure la catégorie "Autre" de l'affichage Diagnostic
    final base = items
        .where((it) => it.category.toLowerCase() != 'autre')
        .toList();

    List<MaintenanceItem> filteredItems;
    final l10n = AppLocalizations.of(context);
    if (_selectedFilter == l10n.all) {
      filteredItems = base;
    } else {
      filteredItems = base.where((item) {
        try {
          final status = provider.getMaintenanceItemStatus(item);
          final statusText = status['status'] as String?;

          if (statusText == null) return false;

          switch (_selectedFilter) {
            case 'Urgent':
              return statusText == 'URGENT' || statusText == 'ERREUR';
            case 'À planifier':
              return statusText == 'BIENTÔT' || statusText == 'PLANIFIER';
            case 'OK':
              return statusText == 'OK';
            default:
              return true;
          }
        } catch (e) {
          // En cas d'erreur, ne pas inclure l'élément
          return false;
        }
      }).toList();
    }

    // Trier selon l'ordre défini dans DefaultMaintenanceItems (même ordre que la page des intervalles)
    final defaultItems = DefaultMaintenanceItems.getDefaultItems();
    final orderMap = <String, int>{};
    for (int i = 0; i < defaultItems.length; i++) {
      orderMap[defaultItems[i].name] = i;
    }

    filteredItems.sort((a, b) {
      final orderA = orderMap[a.name] ?? 999;
      final orderB = orderMap[b.name] ?? 999;
      return orderA.compareTo(orderB);
    });

    return filteredItems;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.diagnostic,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, size: 20),
            onPressed: () => _recreateDatabase(context),
            tooltip: l10n.recreateDatabase,
          ),
        ],
      ),
      body: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.currentVehicle == null) {
            return Center(
              child: Text(l10n.noVehicle, style: const TextStyle(fontSize: 14)),
            );
          }

          return SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Informations du véhicule
                _buildVehicleInfoCard(context, provider),

                const SizedBox(height: 20),

                // Diagnostic des entretiens avec filtre
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          l10n.diagnostic,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                        ),
                        const SizedBox(width: 6),
                        // Indicateur du nombre d'éléments
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 4,
                            vertical: 1,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${_filterMaintenanceItems(provider.maintenanceItems, provider).length}',
                            style: const TextStyle(
                              fontSize: 9,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Filtre d'état moderne et compact
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.primary.withValues(alpha: 0.08),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: DropdownButton<String>(
                        value: _selectedFilter,
                        underline: const SizedBox(),
                        isDense: true,
                        icon: Icon(
                          Icons.filter_list,
                          size: 14,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        style: TextStyle(
                          fontSize: 11,
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                        dropdownColor: Colors.white,
                        menuMaxHeight: 200,
                        items: [
                          DropdownMenuItem(
                            value: 'Tous',
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Text(
                                l10n.all,
                                style: const TextStyle(fontSize: 11),
                              ),
                            ),
                          ),
                          DropdownMenuItem(
                            value: 'Urgent',
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.warning,
                                    size: 12,
                                    color: Colors.red,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    l10n.urgent,
                                    style: const TextStyle(fontSize: 11),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          DropdownMenuItem(
                            value: 'À planifier',
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.schedule,
                                    size: 12,
                                    color: Colors.orange,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    l10n.toSchedule,
                                    style: const TextStyle(fontSize: 11),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          DropdownMenuItem(
                            value: 'OK',
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 12,
                                    color: Colors.green,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    l10n.ok,
                                    style: const TextStyle(fontSize: 11),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedFilter = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Afficher les éléments filtrés
                ..._filterMaintenanceItems(
                  provider.maintenanceItems,
                  provider,
                ).map(
                  (item) =>
                      _buildMaintenanceItemDiagnostic(context, provider, item),
                ),

                // Section Documents (éléments fixes)
                if (provider.documentItems.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Container(
                    key: _documentsKey,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          MdiIcons.fileDocument,
                          color: Theme.of(context).colorScheme.primary,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          l10n.documents,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 11,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Theme.of(context).colorScheme.primary),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                MdiIcons.lock,
                                color: Theme.of(context).colorScheme.primary,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'FIXES',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...provider.documentItems.map(
                    (document) => _buildDocumentItemDiagnostic(context, provider, document),
                  ),
                ],
              ],
            ),
          );
        },
      ),
      floatingActionButton: Consumer<MaintenanceProvider>(
        builder: (context, provider, child) {
          return FloatingActionButton.extended(
            onPressed: () => _navigateToKilometerInput(context),
            icon: Icon(MdiIcons.speedometer, size: 16),
            label: Text(
              l10n.km,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 11),
            ),
            backgroundColor: Theme.of(context).colorScheme.secondary,
            foregroundColor: Colors.white,
            elevation: 2,
            extendedPadding: const EdgeInsets.symmetric(horizontal: 12),
          );
        },
      ),
    );
  }

  // Navigation vers la saisie du kilométrage
  void _navigateToKilometerInput(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const KilometerInputScreen()),
    );
  }

  // Méthode pour recréer la base de données (diagnostic)
  Future<void> _recreateDatabase(BuildContext context) async {
    final l10n = AppLocalizations.of(context);
    final provider = context.read<MaintenanceProvider>();
    try {
      await provider.recreateDatabase();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ ${l10n.databaseRecreated}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Popup pour modifier le kilométrage du dernier entretien
  void _showEditLastMaintenanceKilometersDialog(
    BuildContext context,
    MaintenanceItem item,
  ) async {
    final l10n = AppLocalizations.of(context);
    final provider = context.read<MaintenanceProvider>();

    // Vérifier si l'élément a un dernier entretien enregistré
    if (item.lastMaintenanceKm == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(MdiIcons.informationOutline, color: Colors.white),
              const SizedBox(width: 8),
              Text(l10n.noMaintenanceRecorded),
            ],
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Utiliser le kilométrage du dernier entretien depuis l'item
    final kilometerController = TextEditingController(
      text: item.lastMaintenanceKm.toString(),
    );

    // Essayer de trouver l'historique correspondant pour la date
    final lastMaintenance = provider.maintenanceHistory
        .where((history) => history.maintenanceItemId == item.id)
        .fold<MaintenanceHistory?>(null, (latest, current) {
          if (latest == null) return current;
          return current.maintenanceDate.isAfter(latest.maintenanceDate)
              ? current
              : latest;
        });

    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                l10n.modifyKm,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                item.name,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                lastMaintenance != null
                    ? 'Dernier : ${lastMaintenance.maintenanceDate.day}/${lastMaintenance.maintenanceDate.month}/${lastMaintenance.maintenanceDate.year}'
                    : 'Dernier km : ${item.lastMaintenanceKm} km',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 11,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: kilometerController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
                autofocus: true,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  labelText: l10n.km,
                  suffixText: l10n.km.toLowerCase(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  filled: true,
                  fillColor: Colors.grey[50],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(l10n.cancel, style: const TextStyle(fontSize: 12)),
            ),
            ElevatedButton(
              onPressed: () async {
                final newKm = int.tryParse(kilometerController.text);
                if (newKm != null && newKm >= 0) {
                  try {
                    if (lastMaintenance != null) {
                      // Mettre à jour l'historique existant
                      await provider.updateMaintenanceHistoryKilometers(
                        lastMaintenance.id!,
                        newKm,
                      );
                    } else {
                      // Mettre à jour directement l'item (pour l'instant)
                      final updatedItem = item.copyWith(
                        lastMaintenanceKm: newKm,
                      );
                      await provider.updateMaintenanceItem(updatedItem);
                    }
                    Navigator.of(dialogContext).pop(true);
                  } catch (e) {
                    // En cas d'erreur, fermer le dialog
                    Navigator.of(dialogContext).pop(false);
                  }
                }
              },
              child: Text(l10n.modify, style: const TextStyle(fontSize: 12)),
            ),
          ],
        );
      },
    );

    if (result == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(MdiIcons.checkCircle, color: Colors.white),
              const SizedBox(width: 8),
              Text(l10n.kmUpdated),
            ],
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // Navigation vers l'écran de détail d'entretien
  void _navigateToMaintenanceDetail(
    BuildContext context,
    MaintenanceItem item,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MaintenanceDetailScreen(item: item),
      ),
    );
  }

  // Obtenir l'icône spécifique pour chaque entretien
  IconData _getIconForMaintenanceItem(String maintenanceName) {
    switch (maintenanceName.toLowerCase()) {
      case 'vidange huile moteur':
        return MdiIcons.oilLevel;
      case 'chaîne de distribution':
        return MdiIcons.linkVariant;
      case 'filtre à air':
        return MdiIcons.airFilter;
      case 'filtre à gasoil':
        return MdiIcons.gasStation;
      case 'filtre habitacle':
        return MdiIcons.airConditioner;
      case 'plaquettes de frein':
        return MdiIcons.carBrakeAlert;
      case 'liquide de frein':
        return MdiIcons.carBrakeRetarder;
      case 'pneus':
        return MdiIcons.carTireAlert;
      case 'liquide de refroidissement':
        return MdiIcons.radiator;
      case 'courroie d\'accessoires':
        return MdiIcons.carCruiseControl;
      default:
        return MdiIcons.wrench;
    }
  }

  Widget _buildVehicleInfoCard(
    BuildContext context,
    MaintenanceProvider provider,
  ) {
    final l10n = AppLocalizations.of(context);
    final vehicle = provider.currentVehicle!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  MdiIcons.car,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.vehicle,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildInfoRow(
                    l10n.vehicleName,
                    vehicle.vehicleName,
                    MdiIcons.tag,
                  ),
                ),
                Expanded(
                  child: _buildInfoRow(
                    l10n.kilometers,
                    '${vehicle.currentKilometers} km',
                    MdiIcons.speedometer,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildInfoRow(
                    l10n.brand,
                    vehicle.brand,
                    MdiIcons.carInfo,
                  ),
                ),
                Expanded(
                  child: _buildInfoRow(
                    l10n.lastUpdate,
                    _formatDate(vehicle.lastKmUpdate),
                    MdiIcons.clockOutline,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceItemDiagnostic(
    BuildContext context,
    MaintenanceProvider provider,
    MaintenanceItem item,
  ) {
    final l10n = AppLocalizations.of(context);
    final translationService = TranslationService();
    final status = provider.getMaintenanceItemStatus(item);

    // Créer une clé globale pour cet entretien si elle n'existe pas
    final itemId = item.id?.toString() ?? item.name;
    if (!_itemKeys.containsKey(itemId)) {
      _itemKeys[itemId] = GlobalKey();
    }
    final comparison = status['comparison'] as Map<String, dynamic>;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec nom et statut
            Row(
              key: _itemKeys[itemId], // Clé sur le titre pour un meilleur scroll
              children: [
                Icon(
                  _getIconForMaintenanceItem(item.name),
                  color: Theme.of(context).colorScheme.primary,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    translationService.translateMaintenanceName(item.name, l10n),
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: status['statusColor'].withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: status['statusColor']),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        status['statusIcon'],
                        color: status['statusColor'],
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        status['status'],
                        style: TextStyle(
                          color: status['statusColor'],
                          fontWeight: FontWeight.w600,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Comparaison des kilométrages
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: comparison['isCoherent']
                    ? Colors.green[50]
                    : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: comparison['isCoherent']
                      ? Colors.green[200]!
                      : Colors.red[200]!,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        comparison['isCoherent']
                            ? Icons.check_circle_outline
                            : Icons.error_outline,
                        color: comparison['isCoherent']
                            ? Colors.green[700]
                            : Colors.red[700],
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        l10n.comparisonKm,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                          color: comparison['isCoherent']
                              ? Colors.green[700]
                              : Colors.red[700],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 6),

                  Row(
                    children: [
                      Expanded(
                        child: _buildComparisonItem(
                          l10n.current,
                          '${comparison['currentKm']} km',
                          MdiIcons.speedometer,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildComparisonItem(
                          l10n.last,
                          '${comparison['lastMaintenanceKm']} km',
                          MdiIcons.wrench,
                          Colors.orange,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  _buildComparisonItem(
                    l10n.difference,
                    '${comparison['difference']} km',
                    comparison['difference'] >= 0
                        ? MdiIcons.trendingUp
                        : MdiIcons.trendingDown,
                    comparison['difference'] >= 0 ? Colors.green : Colors.red,
                  ),

                  if (comparison['error'] != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      '⚠️ ${comparison['error']}',
                      style: TextStyle(
                        color: Colors.red[700],
                        fontSize: 10,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Statistiques détaillées
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    l10n.wear,
                    '${status['wearPercentage'].toInt()}%',
                    MdiIcons.gauge,
                    status['statusColor'],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatItem(
                    l10n.remaining,
                    '${status['remainingKm']} km',
                    MdiIcons.roadVariant,
                    status['remainingKm'] > 0 ? Colors.green : Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatItem(
                    l10n.interval,
                    '${item.customIntervalKm ?? item.defaultIntervalKm} km',
                    MdiIcons.repeat,
                    Colors.grey[600]!,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () =>
                        _showEditLastMaintenanceKilometersDialog(context, item),
                    icon: const Icon(Icons.edit, size: 14),
                    label: Text(
                      l10n.modify,
                      style: const TextStyle(fontSize: 10),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        vertical: 6,
                        horizontal: 8,
                      ),
                      minimumSize: const Size(0, 32),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () =>
                        _navigateToMaintenanceDetail(context, item),
                    icon: const Icon(Icons.check_circle, size: 14),
                    label: Text(
                      l10n.performed,
                      style: const TextStyle(fontSize: 10),
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: 6,
                        horizontal: 8,
                      ),
                      minimumSize: const Size(0, 32),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            ),
            Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildComparisonItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 14),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            ),
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 12,
                color: color,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: color,
              fontSize: 11,
            ),
          ),
          Text(label, style: TextStyle(fontSize: 9, color: Colors.grey[600])),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final l10n = AppLocalizations.of(context);
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return l10n.today;
    } else if (difference == 1) {
      return l10n.yesterday;
    } else if (difference < 7) {
      return '${l10n.daysAgo} $difference ${l10n.days}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildDocumentItemDiagnostic(
    BuildContext context,
    MaintenanceProvider provider,
    DocumentItem document,
  ) {
    final l10n = AppLocalizations.of(context);
    final translationService = TranslationService();
    final daysRemaining = document.daysUntilExpiration;
    final isExpired = document.isExpired;
    final isUrgent = document.isUrgent;

    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (isExpired) {
      statusColor = Colors.red;
      statusText = l10n.expired;
      statusIcon = MdiIcons.alertCircle;
    } else if (isUrgent) {
      statusColor = Colors.red;
      statusText = l10n.urgent;
      statusIcon = MdiIcons.alertCircle;
    } else if (daysRemaining <= 15) {
      statusColor = Colors.orange;
      statusText = l10n.soon;
      statusIcon = MdiIcons.clockAlert;
    } else if (daysRemaining <= 30) {
      statusColor = Colors.blue;
      statusText = l10n.planned;
      statusIcon = MdiIcons.calendar;
    } else {
      statusColor = Colors.green;
      statusText = l10n.valid;
      statusIcon = MdiIcons.checkCircle;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.4),
          width: 2,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Card(
        margin: EdgeInsets.zero,
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      statusIcon,
                      color: statusColor,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          translationService.translateDocumentName(document.name, l10n),
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 13,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          document.description,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: statusColor),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          statusIcon,
                          color: statusColor,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          statusText,
                          style: TextStyle(
                            color: statusColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Informations du document - style compact
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  children: [
                    _buildDocumentInfoRow(
                      MdiIcons.calendar,
                      l10n.expiration,
                      document.expirationDate != null
                          ? '${document.expirationDate!.day}/${document.expirationDate!.month}/${document.expirationDate!.year}'
                          : l10n.dateNotDefined,
                      document.expirationDate != null ? Colors.black87 : Colors.orange,
                    ),
                    if (document.expirationDate != null) ...[
                      const SizedBox(height: 6),
                      _buildDocumentInfoRow(
                        MdiIcons.clockOutline,
                        l10n.remains,
                        '${daysRemaining > 0 ? daysRemaining : 0} jours',
                        statusColor,
                      ),
                    ],
                    if (document.companyName != null) ...[
                      const SizedBox(height: 6),
                      _buildDocumentInfoRow(
                        MdiIcons.domain,
                        l10n.company,
                        document.companyName!,
                        Colors.black87,
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 8),

              // Bouton de modification - aligné à gauche, style compact et moderne
              Align(
                alignment: Alignment.centerLeft,
                child: ElevatedButton.icon(
                  onPressed: () => _showDocumentEditDialog(context, provider, document),
                  icon: const Icon(Icons.edit, size: 12),
                  label: Text(
                    l10n.modify,
                    style: const TextStyle(fontSize: 9, fontWeight: FontWeight.w500),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: 4,
                      horizontal: 8,
                    ),
                    minimumSize: const Size(70, 28),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 1,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentInfoRow(
    IconData icon,
    String label,
    String value,
    Color valueColor,
  ) {
    return Row(
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 6),
        Text(
          '$label:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
            fontSize: 10,
          ),
        ),
        const Spacer(),
        Flexible(
          child: Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: valueColor,
              fontSize: 10,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Future<void> _showDocumentEditDialog(
    BuildContext context,
    MaintenanceProvider provider,
    DocumentItem document,
  ) async {
    final TextEditingController companyController =
        TextEditingController(text: document.companyName ?? '');
    final TextEditingController documentNumberController =
        TextEditingController(text: document.documentNumber ?? '');
    final TextEditingController notesController =
        TextEditingController(text: document.notes ?? '');

    DateTime? selectedDate = document.expirationDate;

    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      MdiIcons.fileDocument,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      document.name,
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date d'expiration
                    const Text(
                      'Expiration *',
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () async {
                        // Pour les documents expirés, on permet de sélectionner une date future
                        // mais on s'assure que la date initiale est valide
                        DateTime initialDateToUse;
                        if (selectedDate != null && selectedDate!.isAfter(DateTime.now())) {
                          // Si la date sélectionnée est dans le futur, on l'utilise
                          initialDateToUse = selectedDate!;
                        } else {
                          // Sinon, on utilise une date par défaut dans le futur
                          initialDateToUse = DateTime.now().add(const Duration(days: 365));
                        }

                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: initialDateToUse,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(const Duration(days: 3650)),
                          locale: const Locale('fr', 'FR'),
                        );
                        if (picked != null) {
                          setState(() {
                            selectedDate = picked;
                          });
                        }
                      },
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(MdiIcons.calendar, color: Colors.grey.shade600),
                            const SizedBox(width: 8),
                            Text(
                              selectedDate != null
                                  ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                                  : 'Sélectionner une date',
                              style: TextStyle(
                                color: selectedDate != null ? Colors.black87 : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Compagnie/Organisme
                    const Text(
                      'Compagnie',
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: companyController,
                      decoration: InputDecoration(
                        hintText: document.category == 'Assurance'
                            ? 'Ex: AXA, Allianz, MAIF...'
                            : 'Ex: DEKRA, APAVE, NORISKO...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: Icon(MdiIcons.domain),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Numéro de document
                    Text(
                      'N° ${document.category.toLowerCase()}',
                      style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: documentNumberController,
                      decoration: InputDecoration(
                        hintText: 'Numéro du document',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: Icon(MdiIcons.identifier),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Notes
                    const Text(
                      'Notes',
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: notesController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: 'Notes ou remarques...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: Icon(MdiIcons.noteText),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  child: const Text('Annuler', style: TextStyle(fontSize: 12)),
                ),
                ElevatedButton(
                  onPressed: selectedDate != null ? () async {
                    try {
                      final updatedDocument = document.copyWith(
                        expirationDate: selectedDate,
                        companyName: companyController.text.trim().isEmpty
                            ? null : companyController.text.trim(),
                        documentNumber: documentNumberController.text.trim().isEmpty
                            ? null : documentNumberController.text.trim(),
                        notes: notesController.text.trim().isEmpty
                            ? null : notesController.text.trim(),
                      );

                      await provider.updateDocumentItem(updatedDocument);

                      if (dialogContext.mounted) {
                        Navigator.of(dialogContext).pop();
                      }

                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                Icon(MdiIcons.checkCircle, color: Colors.white),
                                const SizedBox(width: 8),
                                Text('${document.name} mis à jour'),
                              ],
                            ),
                            backgroundColor: Colors.green,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      }
                    } catch (e) {
                      if (dialogContext.mounted) {
                        Navigator.of(dialogContext).pop();
                      }
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                Icon(MdiIcons.alertCircle, color: Colors.white),
                                const SizedBox(width: 8),
                                Text('Erreur: $e'),
                              ],
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  } : null,
                  child: const Text('Enregistrer', style: TextStyle(fontSize: 12)),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
