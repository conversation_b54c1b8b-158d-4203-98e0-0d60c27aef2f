import 'package:flutter/material.dart';

/// Service pour gérer l'état de l'application
class AppStateService {
  static bool _isAppInForeground = false;
  static bool _isNotificationPageVisited = false;
  static DateTime? _lastNotificationPageVisit;

  /// Indique si l'application est au premier plan
  static bool get isAppInForeground => _isAppInForeground;

  /// Indique si la page de notifications a été visitée récemment
  static bool get isNotificationPageVisited => _isNotificationPageVisited;

  /// Dernière visite de la page de notifications
  static DateTime? get lastNotificationPageVisit => _lastNotificationPageVisit;

  /// Met à jour l'état de l'application
  static void setAppState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        _isAppInForeground = true;
        debugPrint('📱 App au premier plan');
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _isAppInForeground = false;
        debugPrint('📱 App en arrière-plan');
        break;
      case AppLifecycleState.hidden:
        _isAppInForeground = false;
        break;
    }
  }

  /// Marque la page de notifications comme visitée
  static void markNotificationPageVisited() {
    _isNotificationPageVisited = true;
    _lastNotificationPageVisit = DateTime.now();
    debugPrint('📋 Page de notifications visitée');
    
    // Réinitialiser après 1 heure
    Future.delayed(const Duration(hours: 1), () {
      _isNotificationPageVisited = false;
    });
  }

  /// Vérifie si les notifications doivent être affichées
  static bool shouldShowNotifications() {
    // Ne pas afficher si l'app est ouverte
    if (_isAppInForeground) {
      debugPrint('🔕 Notifications supprimées - App ouverte');
      return false;
    }

    // Ne pas afficher si la page de notifications a été visitée récemment
    if (_isNotificationPageVisited && _lastNotificationPageVisit != null) {
      final timeSinceVisit = DateTime.now().difference(_lastNotificationPageVisit!);
      if (timeSinceVisit.inHours < 1) {
        debugPrint('🔕 Notifications supprimées - Page visitée récemment');
        return false;
      }
    }

    return true;
  }

  /// Initialise le service
  static void initialize() {
    debugPrint('🔧 AppStateService initialisé');
  }
}
