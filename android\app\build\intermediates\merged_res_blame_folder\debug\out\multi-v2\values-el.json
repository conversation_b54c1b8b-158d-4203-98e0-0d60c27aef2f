{"logs": [{"outputFile": "com.carosti.app-mergeDebugResources-50:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c700a73c2e433e10f13879e3255c75cd\\transformed\\appcompat-1.2.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,8572", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,8653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\80b747fd0bf560516ae3aa3beeee4d5c\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4690", "endColumns": "163", "endOffsets": "4849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3afbe6dd2c3a497e6596084102e039dc\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "54,56,63,75,85,86,87", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5987,6170,6755,7977,8759,8928,9013", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "6055,6252,6833,8114,8923,9008,9088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1e115093ecfdcf00d62fda419f1b05b4\\transformed\\jetified-play-services-ads-23.6.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,287,343,415,491,604,671,838,961,1104,1154,1205,1335,1438,1484,1589,1624,1660,1720,1814,1863", "endColumns": "40,46,55,71,75,112,66,166,122,142,49,50,129,102,45,104,34,35,59,93,48,55", "endOffsets": "239,286,342,414,490,603,670,837,960,1103,1153,1204,1334,1437,1483,1588,1623,1659,1719,1813,1862,1918"}, "to": {"startLines": "60,61,62,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6599,6644,6695,6838,6914,6994,7111,7182,7353,7480,7627,7681,7736,7870,8119,8169,8278,8317,8357,8421,8519,9093", "endColumns": "44,50,59,75,79,116,70,170,126,146,53,54,133,106,49,108,38,39,63,97,52,59", "endOffsets": "6639,6690,6750,6909,6989,7106,7177,7348,7475,7622,7676,7731,7865,7972,8164,8273,8312,8352,8416,8514,8567,9148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1ac570f9a9ce8a2badd3127fcdbc117\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2886,2984,3087,3187,3290,3398,3504,8658", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "2979,3082,3182,3285,3393,3499,3616,8754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dad6ecdc4610e29e4f98bd0a0d7ae39e\\transformed\\browser-1.8.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "6060,6257,6364,6489", "endColumns": "109,106,124,109", "endOffsets": "6165,6359,6484,6594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f200f4a28092d3753dbbadd5663700bb\\transformed\\jetified-play-services-base-18.0.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,482,610,717,894,1015,1129,1230,1415,1519,1685,1810,1984,2125,2190,2248", "endColumns": "106,181,127,106,176,120,113,100,184,103,165,124,173,140,64,57,78", "endOffsets": "299,481,609,716,893,1014,1128,1229,1414,1518,1684,1809,1983,2124,2189,2247,2326"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3621,3732,3918,4050,4161,4342,4467,4585,4854,5043,5151,5321,5450,5628,5773,5842,5904", "endColumns": "110,185,131,110,180,124,117,104,188,107,169,128,177,144,68,61,82", "endOffsets": "3727,3913,4045,4156,4337,4462,4580,4685,5038,5146,5316,5445,5623,5768,5837,5899,5982"}}]}]}