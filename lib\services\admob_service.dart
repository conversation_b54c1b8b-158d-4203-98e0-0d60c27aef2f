import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdMobService {
  static final AdMobService _instance = AdMobService._internal();
  factory AdMobService() => _instance;
  AdMobService._internal();

  // 🎯 VOS IDs PUBLICITAIRES
  static const String _bannerHomeAdUnitId = 'ca-app-pub-4778309361021572/9722088481';
  static const String _bannerStatsAdUnitId = 'ca-app-pub-4778309361021572/3954099947';
  static const String _interstitialAdUnitId = 'ca-app-pub-4778309361021572/6243267657';

  // IDs de test pour le développement
  static const String _testBannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
  static const String _testInterstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712';

  // Variables pour les publicités
  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdReady = false;
  bool _isInitialized = false;

  // Map pour stocker plusieurs bannières par page
  final Map<String, BannerAd> _bannerAds = {};
  final Map<String, bool> _bannerAdsReady = {};

  // Getters pour les IDs (utilise les IDs de test TOUJOURS pour l'instant)
  String getBannerAdUnitId(String page) {
    switch (page) {
      case 'home':
        return _testBannerAdUnitId; // Force test pour l'instant
      case 'stats':
        return _testBannerAdUnitId; // Force test pour l'instant
      default:
        return _testBannerAdUnitId;
    }
  }

  String get interstitialAdUnitId => _testInterstitialAdUnitId; // Force les IDs de test

  // Getters pour l'état des publicités
  bool isBannerAdReady(String page) => _bannerAdsReady[page] ?? false;
  bool get isInterstitialAdReady => _isInterstitialAdReady;
  bool get isInitialized => _isInitialized;
  BannerAd? getBannerAd(String page) => _bannerAds[page];

  /// Initialise le SDK AdMob
  Future<void> initialize() async {
    try {
      debugPrint('🎯 Initialisation AdMob...');
      await MobileAds.instance.initialize();
      _isInitialized = true;
      debugPrint('✅ AdMob initialisé avec succès');

      // Charger les publicités
      await loadBannerAd('home');
      await loadBannerAd('stats');
      await loadBannerAd('global_status');
      // await loadInterstitialAd(); // Désactivé - pas de publicité interstitielle
    } catch (e) {
      debugPrint('❌ Erreur initialisation AdMob: $e');
      _isInitialized = false;
    }
  }

  /// Charge une bannière publicitaire pour une page spécifique
  Future<void> loadBannerAd(String page) async {
    try {
      debugPrint('📱 Chargement bannière publicitaire pour $page...');

      // Disposer l'ancienne bannière si elle existe
      if (_bannerAds[page] != null) {
        _bannerAds[page]!.dispose();
      }

      _bannerAds[page] = BannerAd(
        adUnitId: getBannerAdUnitId(page),
        size: AdSize.banner, // 320x50
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            debugPrint('✅ Bannière $page chargée avec succès');
            _bannerAdsReady[page] = true;
          },
          onAdFailedToLoad: (ad, error) {
            debugPrint('❌ Erreur chargement bannière $page: $error');
            _bannerAdsReady[page] = false;
            ad.dispose();
            _bannerAds.remove(page);
          },
          onAdOpened: (ad) {
            debugPrint('👆 Bannière $page ouverte');
          },
          onAdClosed: (ad) {
            debugPrint('❌ Bannière $page fermée');
          },
        ),
      );

      await _bannerAds[page]!.load();
    } catch (e) {
      debugPrint('❌ Erreur création bannière $page: $e');
      _bannerAdsReady[page] = false;
    }
  }

  /// Charge une publicité interstitielle (DÉSACTIVÉ)
  Future<void> loadInterstitialAd() async {
    debugPrint('🚫 Chargement publicité interstitielle désactivé');
    // Ne charge plus de publicités interstitielles
    _isInterstitialAdReady = false;
  }

  /// Affiche la publicité interstitielle (DÉSACTIVÉ)
  void showInterstitialAd() {
    debugPrint('🚫 Publicité interstitielle désactivée');
    // Ne fait rien - publicités interstitielles désactivées
  }

  /// Dispose les ressources
  void dispose() {
    debugPrint('🗑️ Nettoyage AdMob...');
    for (final banner in _bannerAds.values) {
      banner.dispose();
    }
    _bannerAds.clear();
    _bannerAdsReady.clear();
    _interstitialAd?.dispose();
    _isInterstitialAdReady = false;
  }

  /// Recharge la bannière si nécessaire pour une page
  void reloadBannerIfNeeded(String page) {
    if (!isBannerAdReady(page)) {
      loadBannerAd(page);
    }
  }

  /// Vérifie si les publicités sont supportées sur la plateforme
  bool get isSupported => Platform.isAndroid || Platform.isIOS;
}
