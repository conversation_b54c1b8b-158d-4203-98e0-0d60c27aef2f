import 'package:flutter/material.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import 'presentation_screen.dart';
import '../l10n/app_localizations.dart';

class HelpFaqScreen extends StatefulWidget {
  const HelpFaqScreen({super.key});

  @override
  State<HelpFaqScreen> createState() => _HelpFaqScreenState();
}

class _HelpFaqScreenState extends State<HelpFaqScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showPresentationSection = true;
  List<FaqItem> _faqItems = [];

  List<FaqItem> _getFaqItems(AppLocalizations l10n) {
    return [
      FaqItem(
        question: l10n.addNewVehicleQuestion,
        answer: l10n.addNewVehicleAnswer,
        icon: MdiIcons.car,
        category: l10n.vehicles,
      ),
      FaqItem(
        question: l10n.scheduleMaintenanceQuestion,
        answer: l10n.scheduleMaintenanceAnswer,
        icon: MdiIcons.carWrench,
        category: l10n.maintenance,
      ),
      FaqItem(
        question: l10n.noNotificationsQuestion,
        answer: l10n.noNotificationsAnswer,
        icon: MdiIcons.bellAlert,
        category: l10n.notifications,
      ),
      FaqItem(
        question: l10n.modifyIntervalsQuestion,
        answer: l10n.modifyIntervalsAnswer,
        icon: MdiIcons.timerSettings,
        category: l10n.intervals,
      ),
      FaqItem(
        question: l10n.addCustomOperationQuestion,
        answer: l10n.addCustomOperationAnswer,
        icon: MdiIcons.plus,
        category: l10n.maintenance,
      ),
      FaqItem(
        question: l10n.exportDataQuestion,
        answer: l10n.exportDataAnswer,
        icon: MdiIcons.export,
        category: l10n.data,
      ),
      FaqItem(
        question: l10n.changeCurrencyQuestion,
        answer: l10n.changeCurrencyAnswer,
        icon: MdiIcons.currencyEur,
        category: l10n.parameters,
      ),
      FaqItem(
        question: l10n.wearPercentageQuestion,
        answer: l10n.wearPercentageAnswer,
        icon: MdiIcons.percent,
        category: l10n.diagnostic,
      ),
    ];
  }

  String _selectedCategory = '';
  List<String> _categories = [];

  List<String> _getCategories(AppLocalizations l10n) {
    return [
      l10n.all,
      l10n.vehicles,
      l10n.maintenance,
      l10n.notifications,
      l10n.intervals,
      l10n.data,
      l10n.parameters,
      l10n.diagnostic,
    ];
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // Masquer la section présentation quand on scroll vers le bas
    if (_scrollController.offset > 100 && _showPresentationSection) {
      setState(() {
        _showPresentationSection = false;
      });
    } else if (_scrollController.offset <= 100 && !_showPresentationSection) {
      setState(() {
        _showPresentationSection = true;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  List<FaqItem> _getFilteredFaqItems(AppLocalizations l10n) {
    final faqItems = _getFaqItems(l10n);
    if (_selectedCategory == l10n.all || _selectedCategory.isEmpty) {
      return faqItems;
    }
    return faqItems.where((item) => item.category == _selectedCategory).toList();
  }



  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    // Initialiser les catégories et la sélection si nécessaire
    if (_categories.isEmpty) {
      _categories = _getCategories(l10n);
      _selectedCategory = l10n.all;
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.help,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Section de présentation (conditionnelle)
          if (_showPresentationSection) _buildPresentationSection(l10n),

          // Contenu scrollable
          Expanded(
            child: ListView(
              controller: _scrollController,
              children: [
                // Guide rapide
                _buildQuickGuideSection(l10n),

                // Filtre par catégorie
                _buildCategoryFilter(l10n),

                // Liste des FAQ (convertie en Column pour ListView)
                ..._buildFaqItems(l10n),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresentationSection(AppLocalizations l10n) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            MdiIcons.presentationPlay,
            size: 48,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 12),
          Text(
            l10n.discoverApp,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.discoverAppDesc,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PresentationScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.play_arrow),
              label: Text(l10n.viewPresentation),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickGuideSection(AppLocalizations l10n) {
    final quickSteps = [
      {'icon': MdiIcons.car, 'text': l10n.configureVehicleStep},
      {'icon': MdiIcons.speedometer, 'text': l10n.updateKmStep},
      {'icon': MdiIcons.wrench, 'text': l10n.followMaintenanceStep},
      {'icon': MdiIcons.chartLine, 'text': l10n.consultStatsStep},
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                MdiIcons.rocketLaunch,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                l10n.quickGuide,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              for (int i = 0; i < quickSteps.length; i++) ...[
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          quickSteps[i]['icon'] as IconData,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        quickSteps[i]['text'] as String,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                // Trait horizontal entre les éléments
                if (i < quickSteps.length - 1)
                  Container(
                    width: 20,
                    height: 2,
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(1),
                    ),
                  ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter(AppLocalizations l10n) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;
          
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              backgroundColor: Colors.grey[100],
              selectedColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              labelStyle: TextStyle(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildFaqItems(AppLocalizations l10n) {
    final filteredItems = _getFilteredFaqItems(l10n);

    if (filteredItems.isEmpty) {
      return [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.help_outline,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  l10n.noQuestionFound,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
      ];
    }

    return filteredItems.map((item) =>
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: _buildFaqCard(item),
      )
    ).toList();
  }

  Widget _buildFaqCard(FaqItem item) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      child: ExpansionTile(
        maintainState: true,
        expandedCrossAxisAlignment: CrossAxisAlignment.start,
        childrenPadding: const EdgeInsets.all(0),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            item.icon,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        title: Text(
          item.question,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          item.category,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Text(
              item.answer,
              style: const TextStyle(
                fontSize: 14,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}



class FaqItem {
  final String question;
  final String answer;
  final IconData icon;
  final String category;

  FaqItem({
    required this.question,
    required this.answer,
    required this.icon,
    required this.category,
  });
}
