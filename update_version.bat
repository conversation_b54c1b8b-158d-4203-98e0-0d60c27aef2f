@echo off
echo ========================================
echo   MISE A JOUR AUTOMATIQUE DES VERSIONS
echo ========================================
echo.

REM Demander la nouvelle version
set /p NEW_VERSION="Entrez la nouvelle version (ex: 1.3.0): "
if "%NEW_VERSION%"=="" (
    echo ❌ Version requise
    pause
    exit /b 1
)

REM Demander le nouveau build number
set /p NEW_BUILD="Entrez le nouveau build number (ex: 4): "
if "%NEW_BUILD%"=="" (
    echo ❌ Build number requis
    pause
    exit /b 1
)

echo.
echo 📋 Mise à jour vers:
echo    Version: %NEW_VERSION%
echo    Build: %NEW_BUILD%
echo.

set /p CONFIRM="Confirmer la mise à jour? (o/N): "
if /i not "%CONFIRM%"=="o" (
    echo ❌ Mise à jour annulée
    pause
    exit /b 1
)

echo.
echo 🔄 Mise à jour en cours...

REM 1. Mettre à jour pubspec.yaml
echo 📝 Mise à jour de pubspec.yaml...
powershell -Command "(Get-Content 'pubspec.yaml') -replace 'version: [0-9]+\.[0-9]+\.[0-9]+\+[0-9]+', 'version: %NEW_VERSION%+%NEW_BUILD%' | Set-Content 'pubspec.yaml'"

REM 2. Mettre à jour android/app/build.gradle.kts
echo 📝 Mise à jour de android/app/build.gradle.kts...
powershell -Command "(Get-Content 'android/app/build.gradle.kts') -replace 'versionCode = [0-9]+', 'versionCode = %NEW_BUILD%' | Set-Content 'android/app/build.gradle.kts'"
powershell -Command "(Get-Content 'android/app/build.gradle.kts') -replace 'versionName = \"[0-9]+\.[0-9]+\.[0-9]+\"', 'versionName = \"%NEW_VERSION%\"' | Set-Content 'android/app/build.gradle.kts'"

REM 3. Mettre à jour privacy_policy_web.html
echo 📝 Mise à jour de privacy_policy_web.html...
powershell -Command "(Get-Content 'privacy_policy_web.html') -replace 'v[0-9]+\.[0-9]+\.[0-9]+ \(Build [0-9]+\)', 'v%NEW_VERSION% (Build %NEW_BUILD%)' | Set-Content 'privacy_policy_web.html'"

REM 4. Mettre à jour les valeurs par défaut dans about_screen.dart
echo 📝 Mise à jour des valeurs par défaut dans about_screen.dart...
powershell -Command "(Get-Content 'lib/screens/about_screen.dart') -replace 'String _version = ''[0-9]+\.[0-9]+\.[0-9]+''', 'String _version = ''%NEW_VERSION%''' | Set-Content 'lib/screens/about_screen.dart'"
powershell -Command "(Get-Content 'lib/screens/about_screen.dart') -replace 'String _buildNumber = ''[0-9]+''', 'String _buildNumber = ''%NEW_BUILD%''' | Set-Content 'lib/screens/about_screen.dart'"

REM 5. Mettre à jour les valeurs par défaut dans privacy_policy_screen.dart
echo 📝 Mise à jour des valeurs par défaut dans privacy_policy_screen.dart...
powershell -Command "(Get-Content 'lib/screens/privacy_policy_screen.dart') -replace 'String _version = ''[0-9]+\.[0-9]+\.[0-9]+''', 'String _version = ''%NEW_VERSION%''' | Set-Content 'lib/screens/privacy_policy_screen.dart'"
powershell -Command "(Get-Content 'lib/screens/privacy_policy_screen.dart') -replace 'String _buildNumber = ''[0-9]+''', 'String _buildNumber = ''%NEW_BUILD%''' | Set-Content 'lib/screens/privacy_policy_screen.dart'"

echo.
echo ✅ Mise à jour terminée !
echo.

echo 📋 Fichiers mis à jour:
echo    ✅ pubspec.yaml
echo    ✅ android/app/build.gradle.kts
echo    ✅ privacy_policy_web.html
echo    ✅ lib/screens/about_screen.dart
echo    ✅ lib/screens/privacy_policy_screen.dart
echo.

echo 🚀 Prochaines étapes:
echo    1. Exécutez: flutter pub get
echo    2. Testez l'application
echo    3. Construisez l'AAB: flutter build appbundle --release
echo.

set /p BUILD_NOW="Voulez-vous construire l'AAB maintenant? (o/N): "
if /i "%BUILD_NOW%"=="o" (
    echo.
    echo 🔄 Construction de l'AAB...
    flutter pub get
    flutter build appbundle --release
    echo.
    echo ✅ AAB construit avec succès !
)

echo.
pause
