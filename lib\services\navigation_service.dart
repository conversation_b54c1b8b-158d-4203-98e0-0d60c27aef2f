import 'package:flutter/material.dart';
import '../screens/home_screen.dart';
import '../screens/kilometer_input_screen.dart';
import '../screens/maintenance_diagnostic_screen.dart';

class NavigationService {
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Gère la navigation depuis une notification
  static void handleNotificationTap(String payload) {
    final context = navigatorKey.currentContext;
    if (context == null) return;

    switch (payload) {
      case 'kilometer_reminder':
        // Naviguer vers la page de mise à jour du kilométrage
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const KilometerInputScreen(),
          ),
        );
        break;
      
      case 'maintenance_due':
        // Naviguer vers la page diagnostic avec filtre Urgent
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const MaintenanceDiagnosticScreen(
              initialFilter: 'Urgent',
            ),
          ),
        );
        break;

      case 'maintenance_approaching':
        // Naviguer vers la page diagnostic avec filtre À planifier
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const MaintenanceDiagnosticScreen(
              initialFilter: 'À planifier',
            ),
          ),
        );
        break;
      
      case 'document_expired':
      case 'document_expiring':
        // Naviguer vers la page diagnostic avec scroll vers les documents
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const MaintenanceDiagnosticScreen(
              scrollToDocuments: true,
            ),
          ),
        );
        break;
      
      default:
        // Navigation par défaut vers la page d'accueil
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (route) => false,
        );
        break;
    }
  }

  /// Navigue vers une page spécifique avec des paramètres
  static void navigateToPage(BuildContext context, Widget page) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => page),
    );
  }

  /// Navigue vers la page de mise à jour du kilométrage
  static void navigateToKilometerUpdate(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const KilometerInputScreen()),
    );
  }

  /// Navigue vers la page diagnostic avec un filtre spécifique
  static void navigateToDiagnosticWithFilter(BuildContext context, String filter, {String? focusItemId}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MaintenanceDiagnosticScreen(
          initialFilter: filter,
          focusItemId: focusItemId,
        ),
      ),
    );
  }

  /// Navigue vers la page diagnostic avec scroll vers les documents
  static void navigateToDiagnosticDocuments(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MaintenanceDiagnosticScreen(
          scrollToDocuments: true,
        ),
      ),
    );
  }
}
