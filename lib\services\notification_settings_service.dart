import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/notification_settings.dart';

class NotificationSettingsService {
  static const String _settingsKey = 'notification_settings';
  static NotificationSettings? _cachedSettings;

  /// Charge les paramètres de notification depuis SharedPreferences
  static Future<NotificationSettings> loadSettings() async {
    if (_cachedSettings != null) {
      return _cachedSettings!;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _cachedSettings = NotificationSettings.fromJson(settingsMap);
      } else {
        _cachedSettings = const NotificationSettings(); // Paramètres par défaut
      }
      
      return _cachedSettings!;
    } catch (e) {
      print('Erreur lors du chargement des paramètres de notification: $e');
      _cachedSettings = const NotificationSettings(); // Paramètres par défaut en cas d'erreur
      return _cachedSettings!;
    }
  }

  /// Sauvegarde les paramètres de notification
  static Future<bool> saveSettings(NotificationSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(settings.toJson());
      final success = await prefs.setString(_settingsKey, settingsJson);
      
      if (success) {
        _cachedSettings = settings;
      }
      
      return success;
    } catch (e) {
      print('Erreur lors de la sauvegarde des paramètres de notification: $e');
      return false;
    }
  }

  /// Met à jour un paramètre spécifique
  static Future<bool> updateSetting<T>(String key, T value) async {
    try {
      final currentSettings = await loadSettings();
      NotificationSettings updatedSettings;

      switch (key) {
        case 'notificationsEnabled':
          updatedSettings = currentSettings.copyWith(notificationsEnabled: value as bool);
          break;
        case 'maintenanceNotificationsEnabled':
          updatedSettings = currentSettings.copyWith(maintenanceNotificationsEnabled: value as bool);
          break;
        case 'documentNotificationsEnabled':
          updatedSettings = currentSettings.copyWith(documentNotificationsEnabled: value as bool);
          break;
        case 'dailyDistanceKm':
          updatedSettings = currentSettings.copyWith(dailyDistanceKm: value as double);
          break;
        case 'reminderDaysBefore':
          updatedSettings = currentSettings.copyWith(reminderDaysBefore: value as int);
          break;
        case 'documentReminderDaysBefore':
          updatedSettings = currentSettings.copyWith(documentReminderDaysBefore: value as int);
          break;
        case 'predictiveNotificationsEnabled':
          updatedSettings = currentSettings.copyWith(predictiveNotificationsEnabled: value as bool);
          break;
        case 'predictiveReminderKm':
          updatedSettings = currentSettings.copyWith(predictiveReminderKm: value as int);
          break;
        default:
          return false;
      }

      return await saveSettings(updatedSettings);
    } catch (e) {
      print('Erreur lors de la mise à jour du paramètre $key: $e');
      return false;
    }
  }

  /// Réinitialise les paramètres aux valeurs par défaut
  static Future<bool> resetToDefaults() async {
    const defaultSettings = NotificationSettings();
    return await saveSettings(defaultSettings);
  }

  /// Vérifie si les notifications sont activées globalement
  static Future<bool> areNotificationsEnabled() async {
    final settings = await loadSettings();
    return settings.notificationsEnabled;
  }

  /// Vérifie si les notifications de maintenance sont activées
  static Future<bool> areMaintenanceNotificationsEnabled() async {
    final settings = await loadSettings();
    return settings.notificationsEnabled && settings.maintenanceNotificationsEnabled;
  }

  /// Vérifie si les notifications de documents sont activées
  static Future<bool> areDocumentNotificationsEnabled() async {
    final settings = await loadSettings();
    return settings.notificationsEnabled && settings.documentNotificationsEnabled;
  }

  /// Vérifie si les notifications prédictives sont activées
  static Future<bool> arePredictiveNotificationsEnabled() async {
    final settings = await loadSettings();
    return settings.notificationsEnabled && 
           settings.maintenanceNotificationsEnabled && 
           settings.predictiveNotificationsEnabled;
  }

  /// Obtient la distance quotidienne configurée
  static Future<double> getDailyDistance() async {
    final settings = await loadSettings();
    return settings.dailyDistanceKm;
  }

  /// Obtient le nombre de jours avant rappel pour maintenance
  static Future<int> getMaintenanceReminderDays() async {
    final settings = await loadSettings();
    return settings.reminderDaysBefore;
  }

  /// Obtient le nombre de jours avant rappel pour documents
  static Future<int> getDocumentReminderDays() async {
    final settings = await loadSettings();
    return settings.documentReminderDaysBefore;
  }

  /// Obtient la distance avant rappel prédictif
  static Future<int> getPredictiveReminderKm() async {
    final settings = await loadSettings();
    return settings.predictiveReminderKm;
  }

  /// Invalide le cache (utile après modifications externes)
  static void clearCache() {
    _cachedSettings = null;
  }

  /// Exporte les paramètres pour sauvegarde/partage
  static Future<Map<String, dynamic>> exportSettings() async {
    final settings = await loadSettings();
    return settings.toJson();
  }

  /// Importe les paramètres depuis une sauvegarde
  static Future<bool> importSettings(Map<String, dynamic> settingsMap) async {
    try {
      final settings = NotificationSettings.fromJson(settingsMap);
      return await saveSettings(settings);
    } catch (e) {
      print('Erreur lors de l\'importation des paramètres: $e');
      return false;
    }
  }

  /// Valide les paramètres (utile pour l'interface utilisateur)
  static String? validateDailyDistance(double distance) {
    if (distance < 0) return 'La distance ne peut pas être négative';
    if (distance > 1000) return 'Distance trop élevée (max 1000 km/jour)';
    return null;
  }

  static String? validateReminderDays(int days) {
    if (days < 0) return 'Le nombre de jours ne peut pas être négatif';
    if (days > 365) return 'Nombre de jours trop élevé (max 365 jours)';
    return null;
  }

  static String? validatePredictiveKm(int km) {
    if (km < 0) return 'La distance ne peut pas être négative';
    if (km > 10000) return 'Distance trop élevée (max 10000 km)';
    return null;
  }
}
