import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'providers/maintenance_provider.dart';
import 'providers/notification_provider.dart';
import 'services/theme_service.dart';
import 'services/currency_service.dart';
import 'services/admob_service.dart';
import 'services/connectivity_service.dart';
import 'services/system_notification_service.dart';
import 'services/app_state_service.dart';
import 'services/navigation_service.dart';
import 'services/update_service.dart';
import 'services/premium_service.dart';
import 'services/localization_service.dart';
import 'l10n/app_localizations.dart';
import 'screens/home_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialiser SQLite FFI pour toutes les plateformes non-mobiles
  if (kIsWeb ||
      defaultTargetPlatform == TargetPlatform.windows ||
      defaultTargetPlatform == TargetPlatform.linux ||
      defaultTargetPlatform == TargetPlatform.macOS) {
    // Initialiser FFI
    sqfliteFfiInit();
    // Définir le factory de base de données
    databaseFactory = databaseFactoryFfi;
  }

  // Initialiser AdMob
  await AdMobService().initialize();

  // Initialiser ConnectivityService
  await ConnectivityService().initialize();

  // Initialiser CurrencyService
  await CurrencyService().initialize();

  // Initialiser le service de notifications système
  await SystemNotificationService.initialize();

  // Initialiser le service d'état de l'application
  AppStateService.initialize();

  // Initialiser le service Premium
  await PremiumService().initialize();

  // Initialiser le service de localisation
  await LocalizationService().initialize();

  runApp(const MyCarMaintenanceApp());
}

class MyCarMaintenanceApp extends StatefulWidget {
  const MyCarMaintenanceApp({super.key});

  @override
  State<MyCarMaintenanceApp> createState() => _MyCarMaintenanceAppState();
}

class _MyCarMaintenanceAppState extends State<MyCarMaintenanceApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    AppStateService.setAppState(state);
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => MaintenanceProvider()),
        ChangeNotifierProvider(create: (context) => NotificationProvider()),
        ChangeNotifierProvider(create: (context) => ThemeService()),
        ChangeNotifierProvider(create: (context) => CurrencyService()),
        ChangeNotifierProvider.value(value: LocalizationService()),
      ],
      child: Consumer2<ThemeService, LocalizationService>(
        builder: (context, themeService, localizationService, child) {
          // Configurer la barre de statut en bleu
          SystemChrome.setSystemUIOverlayStyle(
            const SystemUiOverlayStyle(
              statusBarColor: Color(0xFF1E40AF), // Bleu pour la barre de statut
              statusBarIconBrightness: Brightness.light, // Icônes blanches
              statusBarBrightness: Brightness.dark, // Pour iOS
            ),
          );

          return MaterialApp(
            title: 'Carosti',
            debugShowCheckedModeBanner: false,
            navigatorKey: NavigationService.navigatorKey,
            theme: themeService.getLightTheme(),
            themeMode: ThemeMode.light, // Toujours mode clair
            // Configuration de la localisation
            locale: localizationService.currentLocale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: AppLocalizations.supportedLocales,
            home: const AppInitializer(),
            // Support RTL pour l'arabe
            builder: (context, child) {
              return Directionality(
                textDirection: localizationService.isRTL 
                    ? TextDirection.rtl 
                    : TextDirection.ltr,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    if (!mounted) return;

    // Initialiser le service de thème
    final themeService = context.read<ThemeService>();
    await themeService.initialize();

    if (!mounted) return;

    // Initialiser le provider de maintenance
    final maintenanceProvider = context.read<MaintenanceProvider>();
    await maintenanceProvider.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return const HomeScreen();
  }
}
