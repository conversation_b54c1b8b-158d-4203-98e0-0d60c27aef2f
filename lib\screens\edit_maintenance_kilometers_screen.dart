import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/maintenance_provider.dart';
import '../models/maintenance_history.dart';
import '../models/maintenance_item.dart';
import '../services/theme_service.dart';
import '../l10n/app_localizations.dart';

class EditMaintenanceKilometersScreen extends StatefulWidget {
  final MaintenanceHistory history;
  final MaintenanceItem? maintenanceItem;

  const EditMaintenanceKilometersScreen({
    super.key,
    required this.history,
    this.maintenanceItem,
  });

  @override
  State<EditMaintenanceKilometersScreen> createState() =>
      _EditMaintenanceKilometersScreenState();
}

class _EditMaintenanceKilometersScreenState
    extends State<EditMaintenanceKilometersScreen> {
  final _formKey = GlobalKey<FormState>();
  final _kilometerController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _kilometerController.text = widget.history.kilometersAtMaintenance
        .toString();
  }

  @override
  void dispose() {
    _kilometerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Modifier kilométrage'),
        elevation: 0,
        backgroundColor: Colors.transparent,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // Informations de l'entretien
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.04),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: ThemeService
                                .professionalColors['corporate_blue']!
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            _getIconForMaintenanceItem(
                              widget.maintenanceItem?.name ?? '',
                            ),
                            color: ThemeService
                                .professionalColors['corporate_blue']!,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.maintenanceItem?.name ?? 'Entretien',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(fontWeight: FontWeight.w600),
                              ),
                              Text(
                                _formatDate(widget.history.maintenanceDate),
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color: ThemeService
                                          .professionalNeutrals['gray_600']!,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(
                          MdiIcons.speedometer,
                          size: 18,
                          color: ThemeService.professionalSemantics['warning']!,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${widget.history.kilometersAtMaintenance} km',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: ThemeService
                                    .professionalSemantics['warning']!,
                              ),
                        ),
                        const Spacer(),
                        Text(
                          'Actuel',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: ThemeService
                                    .professionalNeutrals['gray_600']!,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Saisie du nouveau kilométrage
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.04),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context).newMileage,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Kilométrage au moment de cet entretien',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeService.professionalNeutrals['gray_600']!,
                      ),
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: _kilometerController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      autofocus: true,
                      textInputAction: TextInputAction.done,
                      onFieldSubmitted: (_) => _updateKilometers(),
                      style: Theme.of(context).textTheme.headlineMedium
                          ?.copyWith(fontWeight: FontWeight.w600),
                      decoration: InputDecoration(
                        hintText: '0',
                        suffixText: 'km',
                        suffixStyle: Theme.of(context).textTheme.titleLarge
                            ?.copyWith(
                              color: ThemeService
                                  .professionalNeutrals['gray_500']!,
                            ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor:
                            ThemeService.professionalNeutrals['gray_50']!,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 20,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Requis';
                        }
                        final km = int.tryParse(value);
                        if (km == null) {
                          return 'Nombre invalide';
                        }
                        if (km < 0) {
                          return 'Doit être positif';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _updateKilometers,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : const Text(
                                'Mettre à jour',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),

              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _updateKilometers() async {
    if (!_formKey.currentState!.validate()) return;

    final newKilometers = int.parse(_kilometerController.text);
    final provider = context.read<MaintenanceProvider>();

    setState(() {
      _isLoading = true;
    });

    try {
      // Mettre à jour le kilométrage de l'entretien
      await provider.updateMaintenanceHistoryKilometers(
        widget.history.id!,
        newKilometers,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(MdiIcons.checkCircle, color: Colors.white),
                const SizedBox(width: 8),
                Text(AppLocalizations.of(context).mileageUpdated),
              ],
            ),
            backgroundColor: ThemeService.professionalSemantics['success']!,
          ),
        );
        Navigator.pop(
          context,
          true,
        ); // Retourner true pour indiquer une modification
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(MdiIcons.alertCircle, color: Colors.white),
                const SizedBox(width: 8),
                Text('Erreur: $e'),
              ],
            ),
            backgroundColor: ThemeService.professionalSemantics['error']!,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  IconData _getIconForMaintenanceItem(String maintenanceName) {
    switch (maintenanceName.toLowerCase()) {
      case 'vidange huile moteur':
        return MdiIcons.oilLevel;
      case 'chaîne de distribution':
        return MdiIcons.linkVariant;
      case 'filtre à air':
        return MdiIcons.airFilter;
      case 'filtre à gasoil':
        return MdiIcons.gasStation;
      case 'filtre habitacle':
        return MdiIcons.airConditioner;
      case 'plaquettes de frein':
        return MdiIcons.carBrakeAlert;
      case 'liquide de frein':
        return MdiIcons.carBrakeRetarder;
      case 'pneus':
        return MdiIcons.carTireAlert;
      case 'liquide de refroidissement':
        return MdiIcons.radiator;
      case 'courroie d\'accessoires':
        return MdiIcons.carCruiseControl;
      default:
        return MdiIcons.wrench;
    }
  }
}
