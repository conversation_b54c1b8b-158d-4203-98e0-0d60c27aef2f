import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'dart:io';
import '../models/notification_settings.dart';
import '../models/vehicle_config.dart';
import '../models/maintenance_item.dart';
import '../models/document_item.dart';
import 'notification_settings_service.dart';
import 'app_state_service.dart';
import 'navigation_service.dart';

class SystemNotificationService {
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  static bool _isInitialized = false;

  // Canaux de notification
  static const String _maintenanceChannelId = 'maintenance_notifications';
  static const String _documentChannelId = 'document_notifications';
  static const String _predictiveChannelId = 'predictive_notifications';
  static const String _urgentChannelId = 'urgent_notifications';

  /// Initialise le service de notifications
  static Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialiser les timezones
      tz.initializeTimeZones();
      // Configuration Android
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      
      // Configuration iOS (si nécessaire)
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // Initialiser le plugin
      final initialized = await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (initialized == true) {
        await _createNotificationChannels();
        await _requestPermissions();
        _isInitialized = true;
        debugPrint('✅ Service de notifications système initialisé');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'initialisation des notifications: $e');
      return false;
    }
  }

  /// Crée les canaux de notification Android
  static Future<void> _createNotificationChannels() async {
    if (!Platform.isAndroid) return;

    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
    if (androidPlugin == null) return;

    // Canal pour les notifications d'entretien
    await androidPlugin.createNotificationChannel(
      const AndroidNotificationChannel(
        _maintenanceChannelId,
        'Entretien Véhicule',
        description: 'Notifications pour les opérations d\'entretien',
        importance: Importance.high,
        playSound: true,
        enableVibration: true,
        showBadge: true,
      ),
    );

    // Canal pour les notifications de documents
    await androidPlugin.createNotificationChannel(
      const AndroidNotificationChannel(
        _documentChannelId,
        'Documents Véhicule',
        description: 'Notifications pour l\'expiration des documents',
        importance: Importance.high,
        playSound: true,
        enableVibration: true,
        showBadge: true,
      ),
    );

    // Canal pour les notifications prédictives
    await androidPlugin.createNotificationChannel(
      const AndroidNotificationChannel(
        _predictiveChannelId,
        'Prévisions Intelligentes',
        description: 'Notifications prédictives basées sur votre usage',
        importance: Importance.defaultImportance,
        playSound: true,
        enableVibration: false,
        showBadge: true,
      ),
    );

    // Canal pour les notifications urgentes
    await androidPlugin.createNotificationChannel(
      const AndroidNotificationChannel(
        _urgentChannelId,
        'Alertes Urgentes',
        description: 'Notifications urgentes nécessitant une attention immédiate',
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
        showBadge: true,
        sound: RawResourceAndroidNotificationSound('notification_urgent'),
      ),
    );
  }

  /// Demande les permissions nécessaires
  static Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      final androidPlugin = _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      if (androidPlugin != null) {
        // Demander la permission pour Android 13+
        final granted = await androidPlugin.requestNotificationsPermission();
        if (granted != true) {
          debugPrint('⚠️ Permission de notification refusée');
          return false;
        }
      }

      // Demander les permissions via permission_handler
      final status = await Permission.notification.request();
      return status.isGranted;
    }
    
    return true; // iOS gère les permissions automatiquement
  }

  /// Gère le tap sur une notification
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('🔔 Notification tappée: ${response.payload}');

    if (response.payload != null) {
      _handleNotificationNavigation(response.payload!);
    }
  }

  /// Gère la navigation basée sur le payload de la notification
  static void _handleNotificationNavigation(String payload) {
    // Cette méthode sera appelée par le NavigationService
    // Le payload sera traité dans le contexte de l'application
    NavigationService.handleNotificationTap(payload);
  }

  /// Planifie les notifications pour un véhicule
  static Future<void> scheduleNotificationsForVehicle({
    required VehicleConfig vehicle,
    required List<MaintenanceItem> maintenanceItems,
    required List<DocumentItem> documentItems,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final settings = await NotificationSettingsService.loadSettings();

    if (!settings.notificationsEnabled) {
      debugPrint('📵 Notifications désactivées');
      return;
    }

    // Vérifier si une notification de rappel kilométrage est nécessaire
    await _scheduleKilometerReminderIfNeeded(vehicle, settings);

    // Annuler toutes les notifications existantes
    await cancelAllNotifications();

    // Planifier les notifications quotidiennes à 10h et 16h
    await _scheduleDailyNotifications(vehicle, maintenanceItems, documentItems, settings);

    debugPrint('✅ Notifications quotidiennes planifiées pour le véhicule ${vehicle.brand} ${vehicle.model}');
  }

  /// Vérifie et planifie une notification de rappel kilométrage si nécessaire
  static Future<void> _scheduleKilometerReminderIfNeeded(
    VehicleConfig vehicle,
    NotificationSettings settings,
  ) async {
    // Vérifier si 15 jours se sont écoulés depuis la dernière mise à jour
    final daysSinceLastUpdate = DateTime.now().difference(vehicle.lastKmUpdate).inDays;

    if (daysSinceLastUpdate >= 15) {
      debugPrint('🔔 Planification notification rappel kilométrage - $daysSinceLastUpdate jours');

      await _notifications.show(
        1000, // ID unique pour les rappels kilométrage
        'Mise à jour kilométrage',
        'Il y a $daysSinceLastUpdate jours depuis votre dernière mise à jour. Pensez à mettre à jour votre kilométrage.',
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'kilometer_reminder',
            'Rappel kilométrage',
            channelDescription: 'Rappels pour mettre à jour le kilométrage',
            importance: Importance.defaultImportance,
            priority: Priority.defaultPriority,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            categoryIdentifier: 'kilometer_reminder',
          ),
        ),
        payload: 'kilometer_reminder',
      );
    }
  }

  /// Planifie les notifications quotidiennes à 10h et 16h
  static Future<void> _scheduleDailyNotifications(
    VehicleConfig vehicle,
    List<MaintenanceItem> maintenanceItems,
    List<DocumentItem> documentItems,
    NotificationSettings settings,
  ) async {
    final now = DateTime.now();

    // Planifier pour les 30 prochains jours
    for (int day = 0; day < 30; day++) {
      final targetDate = now.add(Duration(days: day));

      // Notification du matin à 10h
      final morningTime = DateTime(targetDate.year, targetDate.month, targetDate.day, 10, 0);
      if (morningTime.isAfter(now)) {
        await _scheduleDailyCheck(
          id: 1000 + (day * 2),
          scheduledDate: morningTime,
          vehicle: vehicle,
          maintenanceItems: maintenanceItems,
          documentItems: documentItems,
          settings: settings,
          timeOfDay: 'matin',
        );
      }

      // Notification du soir à 16h
      final eveningTime = DateTime(targetDate.year, targetDate.month, targetDate.day, 16, 0);
      if (eveningTime.isAfter(now)) {
        await _scheduleDailyCheck(
          id: 1000 + (day * 2) + 1,
          scheduledDate: eveningTime,
          vehicle: vehicle,
          maintenanceItems: maintenanceItems,
          documentItems: documentItems,
          settings: settings,
          timeOfDay: 'soir',
        );
      }
    }
  }

  /// Planifie une vérification quotidienne
  static Future<void> _scheduleDailyCheck({
    required int id,
    required DateTime scheduledDate,
    required VehicleConfig vehicle,
    required List<MaintenanceItem> maintenanceItems,
    required List<DocumentItem> documentItems,
    required NotificationSettings settings,
    required String timeOfDay,
  }) async {
    // Vérifier s'il y a des alertes à cette date
    final hasAlerts = _checkForAlerts(vehicle, maintenanceItems, documentItems, settings);

    if (hasAlerts) {
      // Vérifier si les notifications doivent être affichées
      if (AppStateService.shouldShowNotifications()) {
        await _scheduleNotification(
          id: id,
          title: '🔔 Rappel d\'entretien',
          body: 'Vérifiez vos entretiens en cours',
          scheduledDate: scheduledDate,
          channelId: _maintenanceChannelId,
          payload: 'daily_check_$timeOfDay',
        );
      } else {
        debugPrint('🔕 Notification supprimée - App ouverte ou page visitée');
      }
    }
  }

  /// Vérifie s'il y a des alertes à afficher
  static bool _checkForAlerts(
    VehicleConfig vehicle,
    List<MaintenanceItem> maintenanceItems,
    List<DocumentItem> documentItems,
    NotificationSettings settings,
  ) {
    // Vérifier les entretiens
    for (final item in maintenanceItems) {
      if (!item.isActive) continue;
      // Exclure les entretiens de type "autre" et les entretiens personnalisés
      if (item.category.toLowerCase() == 'autre') continue;
      if (item.name.startsWith('[CUSTOM]')) continue;

      // Alerte si entretien en retard ou approche
      if (item.isMaintenanceDue || item.isMaintenanceApproaching) {
        return true;
      }
    }

    // Vérifier les documents
    final now = DateTime.now();
    for (final document in documentItems) {
      if (document.expirationDate == null) continue;

      final daysUntilExpiration = document.expirationDate!.difference(now).inDays;
      if (daysUntilExpiration <= settings.documentReminderDaysBefore) {
        return true;
      }
    }

    return false;
  }





  /// Affiche une notification immédiate
  static Future<void> _showImmediateNotification({
    required int id,
    required String title,
    required String body,
    required String channelId,
    String? payload,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'immediate_notifications',
      'Notifications Immédiates',
      channelDescription: 'Notifications affichées immédiatement',
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      enableVibration: true,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(id, title, body, details, payload: payload);
  }

  /// Planifie une notification pour une date future
  static Future<void> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    required String channelId,
    String? payload,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      _getChannelName(channelId),
      channelDescription: _getChannelDescription(channelId),
      importance: _getChannelImportance(channelId),
      priority: Priority.high,
      playSound: true,
      enableVibration: channelId == _urgentChannelId,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Convertir DateTime en TZDateTime
    final tzScheduledDate = tz.TZDateTime.from(scheduledDate, tz.local);

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tzScheduledDate,
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  /// Annule toutes les notifications
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
    debugPrint('🗑️ Toutes les notifications ont été annulées');
  }

  /// Annule une notification spécifique
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  /// Obtient les notifications en attente
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  /// Vérifie si les notifications sont autorisées
  static Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      final androidPlugin = _notifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
      if (androidPlugin != null) {
        return await androidPlugin.areNotificationsEnabled() ?? false;
      }
    }
    return true;
  }

  /// Ouvre les paramètres de notification du système
  static Future<void> openNotificationSettings() async {
    if (Platform.isAndroid) {
      await openAppSettings();
    }
  }

  /// Méthodes utilitaires privées
  static String _getChannelName(String channelId) {
    switch (channelId) {
      case _maintenanceChannelId:
        return 'Entretien Véhicule';
      case _documentChannelId:
        return 'Documents Véhicule';
      case _predictiveChannelId:
        return 'Prévisions Intelligentes';
      case _urgentChannelId:
        return 'Alertes Urgentes';
      default:
        return 'Notifications';
    }
  }

  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case _maintenanceChannelId:
        return 'Notifications pour les opérations d\'entretien';
      case _documentChannelId:
        return 'Notifications pour l\'expiration des documents';
      case _predictiveChannelId:
        return 'Notifications prédictives basées sur votre usage';
      case _urgentChannelId:
        return 'Notifications urgentes nécessitant une attention immédiate';
      default:
        return 'Notifications de l\'application';
    }
  }

  static Importance _getChannelImportance(String channelId) {
    switch (channelId) {
      case _urgentChannelId:
        return Importance.max;
      case _maintenanceChannelId:
      case _documentChannelId:
        return Importance.high;
      case _predictiveChannelId:
        return Importance.defaultImportance;
      default:
        return Importance.defaultImportance;
    }
  }



  /// Teste les notifications (pour debug)
  static Future<void> testNotification() async {
    await _showImmediateNotification(
      id: 9999,
      title: '🧪 Test de notification',
      body: 'Ceci est un test du système de notifications',
      channelId: _maintenanceChannelId,
      payload: 'test_notification',
    );
  }
}
